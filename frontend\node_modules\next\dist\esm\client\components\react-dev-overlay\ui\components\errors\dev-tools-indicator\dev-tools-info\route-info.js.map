{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/route-info.tsx"], "sourcesContent": ["import type { HTMLProps } from 'react'\nimport type { DevToolsInfoPropsCore } from './dev-tools-info'\nimport { DevToolsInfo } from './dev-tools-info'\n\nfunction StaticRouteContent({ routerType }: { routerType: 'pages' | 'app' }) {\n  return (\n    <article className=\"dev-tools-info-article\">\n      <p className=\"dev-tools-info-paragraph\">\n        The path{' '}\n        <code className=\"dev-tools-info-code\">{window.location.pathname}</code>{' '}\n        is marked as \"static\" since it will be prerendered during the build\n        time.\n      </p>\n      <p className=\"dev-tools-info-paragraph\">\n        With Static Rendering, routes are rendered at build time, or in the\n        background after{' '}\n        <a\n          className=\"dev-tools-info-link\"\n          href={\n            routerType === 'pages'\n              ? 'https://nextjs.org/docs/pages/building-your-application/data-fetching/incremental-static-regeneration'\n              : `https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration`\n          }\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          data revalidation\n        </a>\n        .\n      </p>\n      <p className=\"dev-tools-info-paragraph\">\n        Static rendering is useful when a route has data that is not\n        personalized to the user and can be known at build time, such as a\n        static blog post or a product page.\n      </p>\n    </article>\n  )\n}\n\nfunction DynamicRouteContent({ routerType }: { routerType: 'pages' | 'app' }) {\n  return (\n    <article className=\"dev-tools-info-article\">\n      <p className=\"dev-tools-info-paragraph\">\n        The path{' '}\n        <code className=\"dev-tools-info-code\">{window.location.pathname}</code>{' '}\n        is marked as \"dynamic\" since it will be rendered for each user at{' '}\n        <strong>request time</strong>.\n      </p>\n      <p className=\"dev-tools-info-paragraph\">\n        Dynamic rendering is useful when a route has data that is personalized\n        to the user or has information that can only be known at request time,\n        such as cookies or the URL's search params.\n      </p>\n      {routerType === 'pages' ? (\n        <p className=\"dev-tools-info-pagraph\">\n          Exporting the{' '}\n          <a\n            className=\"dev-tools-info-link\"\n            href=\"https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            getServerSideProps\n          </a>{' '}\n          function will opt the route into dynamic rendering. This function will\n          be called by the server on every request.\n        </p>\n      ) : (\n        <p className=\"dev-tools-info-paragraph\">\n          During rendering, if a{' '}\n          <a\n            className=\"dev-tools-info-link\"\n            href=\"https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-apis\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            Dynamic API\n          </a>{' '}\n          or a{' '}\n          <a\n            className=\"dev-tools-info-link\"\n            href=\"https://nextjs.org/docs/app/api-reference/functions/fetch\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            fetch\n          </a>{' '}\n          option of{' '}\n          <code className=\"dev-tools-info-code\">{`{ cache: 'no-store' }`}</code>{' '}\n          is discovered, Next.js will switch to dynamically rendering the whole\n          route.\n        </p>\n      )}\n    </article>\n  )\n}\n\nconst learnMoreLink = {\n  pages: {\n    static:\n      'https://nextjs.org/docs/pages/building-your-application/rendering/static-site-generation',\n    dynamic:\n      'https://nextjs.org/docs/pages/building-your-application/rendering/server-side-rendering',\n  },\n  app: {\n    static:\n      'https://nextjs.org/docs/app/building-your-application/rendering/server-components#static-rendering-default',\n    dynamic:\n      'https://nextjs.org/docs/app/building-your-application/rendering/server-components#dynamic-rendering',\n  },\n} as const\n\nexport function RouteInfo({\n  routeType,\n  routerType,\n  ...props\n}: {\n  routeType: 'Static' | 'Dynamic'\n  routerType: 'pages' | 'app'\n} & DevToolsInfoPropsCore &\n  HTMLProps<HTMLDivElement>) {\n  const isStaticRoute = routeType === 'Static'\n\n  const learnMore = isStaticRoute\n    ? learnMoreLink[routerType].static\n    : learnMoreLink[routerType].dynamic\n\n  return (\n    <DevToolsInfo\n      title={`${routeType} Route`}\n      learnMoreLink={learnMore}\n      {...props}\n    >\n      {isStaticRoute ? (\n        <StaticRouteContent routerType={routerType} />\n      ) : (\n        <DynamicRouteContent routerType={routerType} />\n      )}\n    </DevToolsInfo>\n  )\n}\n\nexport const DEV_TOOLS_INFO_ROUTE_INFO_STYLES = ``\n"], "names": ["DevToolsInfo", "StaticRouteContent", "routerType", "article", "className", "p", "code", "window", "location", "pathname", "a", "href", "target", "rel", "DynamicRouteContent", "strong", "learnMoreLink", "pages", "static", "dynamic", "app", "RouteInfo", "routeType", "props", "isStaticRoute", "learnMore", "title", "DEV_TOOLS_INFO_ROUTE_INFO_STYLES"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,mBAAkB;AAE/C,SAASC,mBAAmB,KAA+C;IAA/C,IAAA,EAAEC,UAAU,EAAmC,GAA/C;IAC1B,qBACE,MAACC;QAAQC,WAAU;;0BACjB,MAACC;gBAAED,WAAU;;oBAA2B;oBAC7B;kCACT,KAACE;wBAAKF,WAAU;kCAAuBG,OAAOC,QAAQ,CAACC,QAAQ;;oBAAS;oBAAI;;;0BAI9E,MAACJ;gBAAED,WAAU;;oBAA2B;oBAErB;kCACjB,KAACM;wBACCN,WAAU;wBACVO,MACET,eAAe,UACX,0GACC;wBAEPU,QAAO;wBACPC,KAAI;kCACL;;oBAEG;;;0BAGN,KAACR;gBAAED,WAAU;0BAA2B;;;;AAO9C;AAEA,SAASU,oBAAoB,KAA+C;IAA/C,IAAA,EAAEZ,UAAU,EAAmC,GAA/C;IAC3B,qBACE,MAACC;QAAQC,WAAU;;0BACjB,MAACC;gBAAED,WAAU;;oBAA2B;oBAC7B;kCACT,KAACE;wBAAKF,WAAU;kCAAuBG,OAAOC,QAAQ,CAACC,QAAQ;;oBAAS;oBAAI;oBACV;kCAClE,KAACM;kCAAO;;oBAAqB;;;0BAE/B,KAACV;gBAAED,WAAU;0BAA2B;;YAKvCF,eAAe,wBACd,MAACG;gBAAED,WAAU;;oBAAyB;oBACtB;kCACd,KAACM;wBACCN,WAAU;wBACVO,MAAK;wBACLC,QAAO;wBACPC,KAAI;kCACL;;oBAEI;oBAAI;;+BAKX,MAACR;gBAAED,WAAU;;oBAA2B;oBACf;kCACvB,KAACM;wBACCN,WAAU;wBACVO,MAAK;wBACLC,QAAO;wBACPC,KAAI;kCACL;;oBAEI;oBAAI;oBACJ;kCACL,KAACH;wBACCN,WAAU;wBACVO,MAAK;wBACLC,QAAO;wBACPC,KAAI;kCACL;;oBAEI;oBAAI;oBACC;kCACV,KAACP;wBAAKF,WAAU;kCAAwB;;oBAA+B;oBAAI;;;;;AAOrF;AAEA,MAAMY,gBAAgB;IACpBC,OAAO;QACLC,QACE;QACFC,SACE;IACJ;IACAC,KAAK;QACHF,QACE;QACFC,SACE;IACJ;AACF;AAEA,OAAO,SAASE,UAAU,KAQC;IARD,IAAA,EACxBC,SAAS,EACTpB,UAAU,EACV,GAAGqB,OAKsB,GARD;IASxB,MAAMC,gBAAgBF,cAAc;IAEpC,MAAMG,YAAYD,gBACdR,aAAa,CAACd,WAAW,CAACgB,MAAM,GAChCF,aAAa,CAACd,WAAW,CAACiB,OAAO;IAErC,qBACE,KAACnB;QACC0B,OAAO,AAAC,KAAEJ,YAAU;QACpBN,eAAeS;QACd,GAAGF,KAAK;kBAERC,8BACC,KAACvB;YAAmBC,YAAYA;2BAEhC,KAACY;YAAoBZ,YAAYA;;;AAIzC;AAEA,OAAO,MAAMyB,mCAAoC,GAAC"}