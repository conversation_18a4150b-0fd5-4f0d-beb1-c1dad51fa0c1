"use client";

import { Image } from "@nextui-org/react";

import { useEffect, useState } from "react";
import { fetcher } from "@/lib/utils";

export interface LocationResponse {
    status: string;
    country: string;
    countryCode: string;
    region: string;
    regionName: string;
    city: string;
    zip: string;
    lat: number;
    lon: number;
    timezone: string;
    isp: string;
    org: string;
    as: string;
    query: string;
}



// export async function getLocation() {
//     try {
//         const response = await fetch("http://ip-api.com/json/");
//         const json = (await response.json()) as LocationResponse;
//         if (typeof json.lat === "number" && typeof json.lon === "number") {
//             return [json.lon, json.lat];
//         }
//         // eslint-disable-next-line no-empty
//     } catch {}
//     return middleOfUSA;
// }

// const STATIC_LOCATION = [-98.5795, 39.8283];

// export default function YouAreHere() {
//     const [popupLocation] = useState(STATIC_LOCATION);
//     const { current: map } = useMap();

//     if (!map) return null;

//     console.log(popupLocation);
//     map.flyTo({ center: popupLocation, zoom: 12 });

//     return (
//         <Popup longitude={popupLocation[0]} latitude={popupLocation[1]}>
//             <h3>You are approximately here!</h3>
//         </Popup>
//     );
// }
type Props = {
    id: string;
};

type AlertRequest = {
    created_at: string;
    description: string;
    emergency_type: string;
    id: number;
    images: { id: number; image: string }[];
    location: string;
    user_first_name: string;
    user_last_name: string;
};

export function AlertItemDetails({ id }: Props) {
    const [isLoading, setIsLoading] = useState(true);
    const [data, setData] = useState<AlertRequest | null>(null);
    console.log(data);

    useEffect(() => {
        fetcher(`/emergency/${id}/`, null, "GET")
            .then((res) => res.json())
            .then((data) => {
                setData(data as AlertRequest);
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [id]);

    return (
        <div className="container mx-auto px-4 py-6 space-y-6" dir="rtl">
            {isLoading ? (
                <div className="flex items-center justify-center min-h-[20rem]">
                    <p className="font-bold text-xl">جارى التحميل...</p>
                </div>
            ) : data ? (
                <>
                    <h1 className="text-xl font-bold text-right mt-4">
                        {data.location}
                    </h1>

                    <p className="text-sm text-gray-600 leading-relaxed text-right">
                        {data.description}
                    </p>

                    <div className="text-right">
                        <h2 className="text-sm font-semibold mb-3">
                            الصور المرفقة
                        </h2>
                        <div
                            className="grid grid-cols-3 gap-4 overflow-x-auto pb-4"
                            dir="ltr"
                        >
                            {data.images.map(({ id, image }) => (
                                <Image
                                    key={id}
                                    src={image || "/placeholder.svg"}
                                    alt={`صورة ${id + 1}`}
                                    className="w-[240px] h-[240px] object-cover rounded-lg"
                                />
                            ))}
                        </div>
                    </div>
                </>
            ) : (
                <p>حدث خطأ أثناء تحميل الطلب برجاء اعادة المحاولة لاحقا</p>
            )}
        </div>
    );
}
