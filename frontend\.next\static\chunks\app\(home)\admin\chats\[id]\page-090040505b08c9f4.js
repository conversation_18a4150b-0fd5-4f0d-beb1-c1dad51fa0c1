(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[393],{3668:(e,s,r)=>{"use strict";r.d(s,{y:()=>t});var a=r(71153);let t=a.Ik({message:a.Yj().min(1,"يرجى إدخال رسالة")})},24595:(e,s,r)=>{Promise.resolve().then(r.bind(r,99467))},27290:(e,s,r)=>{"use strict";r.d(s,{Z:()=>k});var a=r(65262),t=r(69478),l=r(66232),n=(0,t.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...l.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),o=r(12115),d=r(73750),i=r(81627),c=r(77151),u=r(9906),m=r(88629),h=r(75894),b=r(56973),x=r(5712),f=r(81467),v=r(672),g=r(491),p=r(6548),j=r(35925),y=r(9539),N=r(95155),w=(0,b.Rf)((e,s)=>{let{children:r,context:t,Component:l,isPressable:w,disableAnimation:k,disableRipple:P,getCardProps:S,getRippleProps:C}=function(e){var s,r,a,t;let l=(0,h.o)(),[y,N]=(0,b.rE)(e,n.variantKeys),{ref:w,as:k,children:P,onClick:S,onPress:C,autoFocus:E,className:A,classNames:D,allowTextSelectionOnPress:R=!0,...M}=y,W=(0,p.zD)(w),F=k||(e.isPressable?"button":"div"),I="string"==typeof F,z=null!=(r=null!=(s=e.disableAnimation)?s:null==l?void 0:l.disableAnimation)&&r,B=null!=(t=null!=(a=e.disableRipple)?a:null==l?void 0:l.disableRipple)&&t,H=(0,x.$)(null==D?void 0:D.base,A),{onClear:_,onPress:O,ripples:T}=(0,j.k)(),$=(0,o.useCallback)(e=>{B||z||W.current&&O(e)},[B,z,W,O]),{buttonProps:U,isPressed:G}=(0,m.l)({onPress:(0,d.c)(C,$),elementType:k,isDisabled:!e.isPressable,onClick:S,allowTextSelectionOnPress:R,...M},W),{hoverProps:L,isHovered:V}=(0,u.M)({isDisabled:!e.isHoverable,...M}),{isFocusVisible:J,isFocused:Z,focusProps:q}=(0,c.o)({autoFocus:E}),K=(0,o.useMemo)(()=>n({...N,disableAnimation:z}),[(0,f.t6)(N),z]),Q=(0,o.useMemo)(()=>({slots:K,classNames:D,disableAnimation:z,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[K,D,e.isDisabled,e.isFooterBlurred,z,e.fullWidth]),Y=(0,o.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:W,className:K.base({class:H}),tabIndex:e.isPressable?0:-1,"data-hover":(0,v.sE)(V),"data-pressed":(0,v.sE)(G),"data-focus":(0,v.sE)(Z),"data-focus-visible":(0,v.sE)(J),"data-disabled":(0,v.sE)(e.isDisabled),...(0,i.v)(e.isPressable?{...U,...q,role:"button"}:{},e.isHoverable?L:{},(0,g.$)(M,{enabled:I}),(0,g.$)(s))}},[W,K,H,I,e.isPressable,e.isHoverable,e.isDisabled,V,G,J,U,q,L,M]),X=(0,o.useCallback)(()=>({ripples:T,onClear:_}),[T,_]);return{context:Q,domRef:W,Component:F,classNames:D,children:P,isHovered:V,isPressed:G,disableAnimation:z,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:B,handlePress:$,isFocusVisible:J,getCardProps:Y,getRippleProps:X}}({...e,ref:s});return(0,N.jsxs)(l,{...S(),children:[(0,N.jsx)(a.u,{value:t,children:r}),w&&!k&&!P&&(0,N.jsx)(y.j,{...C()})]})});w.displayName="NextUI.Card";var k=w},35695:(e,s,r)=>{"use strict";var a=r(18999);r.o(a,"notFound")&&r.d(s,{notFound:function(){return a.notFound}}),r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},40157:(e,s,r)=>{"use strict";r.d(s,{A:()=>d});var a=r(12115);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return s.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,s)=>{let{color:r="currentColor",size:t=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:i="",children:c,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:s,...n,width:t,height:t,stroke:r,strokeWidth:d?24*Number(o)/Number(t):o,className:l("lucide",i),...m},[...u.map(e=>{let[s,r]=e;return(0,a.createElement)(s,r)}),...Array.isArray(c)?c:[c]])}),d=(e,s)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:d,...i}=r;return(0,a.createElement)(o,{ref:n,iconNode:s,className:l("lucide-".concat(t(e)),d),...i})});return r.displayName="".concat(e),r}},54736:(e,s,r)=>{"use strict";r.d(s,{U:()=>i});var a=r(65262),t=r(56973),l=r(6548),n=r(5712),o=r(95155),d=(0,t.Rf)((e,s)=>{var r;let{as:t,className:d,children:i,...c}=e,u=(0,l.zD)(s),{slots:m,classNames:h}=(0,a.f)(),b=(0,n.$)(null==h?void 0:h.body,d);return(0,o.jsx)(t||"div",{ref:u,className:null==(r=m.body)?void 0:r.call(m,{class:b}),...c,children:i})});d.displayName="NextUI.CardBody";var i=d},59434:(e,s,r)=>{"use strict";r.d(s,{G:()=>n,cn:()=>l,f:()=>o});var a=r(52596),t=r(39688);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}async function n(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",a=arguments.length>3?arguments[3]:void 0;return await fetch("http://localhost:8000"+e,{method:r,body:null===s?null:JSON.stringify(s),headers:{"Content-Type":"application/json",Authorization:a?"Bearer ".concat(a):""}})}let o=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},65262:(e,s,r)=>{"use strict";r.d(s,{f:()=>t,u:()=>a});var[a,t]=(0,r(42810).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},66575:(e,s,r)=>{"use strict";r.d(s,{d:()=>i});var a=r(65262),t=r(56973),l=r(6548),n=r(5712),o=r(95155),d=(0,t.Rf)((e,s)=>{var r;let{as:t,className:d,children:i,...c}=e,u=(0,l.zD)(s),{slots:m,classNames:h}=(0,a.f)(),b=(0,n.$)(null==h?void 0:h.header,d);return(0,o.jsx)(t||"div",{ref:u,className:null==(r=m.header)?void 0:r.call(m,{class:b}),...c,children:i})});d.displayName="NextUI.CardHeader";var i=d},73672:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(40157).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},93176:(e,s,r)=>{"use strict";r.d(s,{r:()=>i});var a=r(76917),t=r(1529),l=r(12115),n=r(56973),o=r(95155),d=(0,n.Rf)((e,s)=>{let{Component:r,label:n,description:d,isClearable:i,startContent:c,endContent:u,labelPlacement:m,hasHelper:h,isOutsideLeft:b,shouldLabelBeOutside:x,errorMessage:f,isInvalid:v,getBaseProps:g,getLabelProps:p,getInputProps:j,getInnerWrapperProps:y,getInputWrapperProps:N,getMainWrapperProps:w,getHelperWrapperProps:k,getDescriptionProps:P,getErrorMessageProps:S,getClearButtonProps:C}=(0,a.G)({...e,ref:s}),E=n?(0,o.jsx)("label",{...p(),children:n}):null,A=(0,l.useMemo)(()=>i?(0,o.jsx)("button",{...C(),children:u||(0,o.jsx)(t.o,{})}):u,[i,C]),D=(0,l.useMemo)(()=>{let e=v&&f,s=e||d;return h&&s?(0,o.jsx)("div",{...k(),children:e?(0,o.jsx)("div",{...S(),children:f}):(0,o.jsx)("div",{...P(),children:d})}):null},[h,v,f,d,k,S,P]),R=(0,l.useMemo)(()=>(0,o.jsxs)("div",{...y(),children:[c,(0,o.jsx)("input",{...j()}),A]}),[c,A,j,y]),M=(0,l.useMemo)(()=>x?(0,o.jsxs)("div",{...w(),children:[(0,o.jsxs)("div",{...N(),children:[b?null:E,R]}),D]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{...N(),children:[E,R]}),D]}),[m,D,x,E,R,f,d,w,N,S,P]);return(0,o.jsxs)(r,{...g(),children:[b?E:null,M]})});d.displayName="NextUI.Input";var i=d},99467:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j,dynamic:()=>g});var a=r(95155),t=r(59434),l=r(90221);let n=(0,r(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var o=r(73672),d=r(35695),i=r(12115),c=r(82842),u=r(62177),m=r(3668),h=r(27290),b=r(66575),x=r(54736),f=r(93176),v=r(66146);let g="force-dynamic";function p(){let[e]=(0,c.lT)(["access"]),s=(0,d.useParams)(),r=(0,d.useSearchParams)(),g=(0,d.useRouter)(),p=s.id,j=r.get("room"),y=(0,i.useRef)(null),[N,w]=(0,i.useState)([]),[k,P]=(0,i.useState)(!1),S=(0,i.useRef)(null),C=e.access,{control:E,handleSubmit:A,formState:{errors:D,isSubmitting:R},reset:M}=(0,u.mN)({resolver:(0,l.u)(m.y),defaultValues:{message:""}}),W=()=>{var e;null==(e=y.current)||e.scrollIntoView({behavior:"smooth"})};return((0,i.useEffect)(()=>{if(C&&j){(0,t.G)("/chats/".concat(p,"/messages"),null,"GET",C).then(e=>e.json()).then(e=>{w(e.results)}).catch(e=>{console.error("Error fetching room info:",e)});let e=new WebSocket("ws://localhost:8000/ws/chat/".concat(j,"/?token=").concat(C));return S.current=e,e.onopen=()=>{console.log("WebSocket connected"),P(!0)},e.onmessage=e=>{let s=JSON.parse(e.data);console.log(s),w(e=>[{id:Math.random(),sender:s.user,body:s.message,chat_room:j,created_at:new Date().toISOString()},...e])},e.onclose=()=>{console.log("WebSocket disconnected"),P(!1)},()=>{e.close()}}},[C,j,p]),(0,i.useEffect)(()=>{W()},[N]),p&&j)?(0,a.jsx)("div",{className:"container mx-auto py-6 max-w-4xl",children:(0,a.jsxs)(h.Z,{className:"border shadow-md h-[calc(100vh-100px)] flex flex-col",children:[(0,a.jsx)(b.d,{className:"bg-gray-50 dark:bg-gray-800 border-b px-4 py-3 flex flex-row items-center justify-between space-y-0",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.T,{onPress:()=>g.push("/admin/chats"),className:"mr-2",children:(0,a.jsx)(n,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg",children:"اسم المستخدم"}),(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["رقم الغرفة: ",j]})})]})]})}),(0,a.jsx)(x.U,{className:"flex-1 fle overflow-y-auto p-4 space-y-4",children:0===N.length?(0,a.jsx)("div",{className:"grid place-content-center h-full",children:(0,a.jsx)("p",{className:"text-gray-500",children:"لا يوجد رسائل بعد"})}):(0,a.jsxs)("div",{className:"flex flex-col-reverse gap-3",children:[N.map(e=>(0,a.jsx)("div",{className:"flex ".concat(1===e.sender?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"max-w-[70%] px-4 py-2 rounded-lg ".concat(1===e.sender?"bg-blue-600 text-white rounded-br-none":"bg-gray-100 dark:bg-gray-800 rounded-bl-none"),children:[(0,a.jsx)("p",{className:"text-sm",children:e.body}),(0,a.jsx)("span",{className:"text-xs mt-1 block text-right ".concat(1===e.sender?"text-blue-100":"text-gray-500"),children:(0,t.f)(e.created_at)})]})},e.id)),(0,a.jsx)("div",{ref:y})]})}),(0,a.jsxs)("div",{className:"p-4 border-t",children:[(0,a.jsxs)("form",{className:"flex gap-2",onSubmit:A(e=>{var s;(null==(s=S.current)?void 0:s.readyState)===WebSocket.OPEN&&(S.current.send(JSON.stringify({message:e.message})),M())}),children:[(0,a.jsx)(u.xI,{name:"message",control:E,render:e=>{let{field:s}=e;return(0,a.jsx)(f.r,{...s,placeholder:"قم بكتابة رسالتك هنا...",className:"flex-1",disabled:!k})}}),(0,a.jsxs)(v.T,{type:"submit",disabled:R||!k,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"ارسال"]})]}),D.message&&(0,a.jsx)("p",{className:"text-red-500 text-xs mt-1",children:D.message.message})]})]})}):(0,d.notFound)()}function j(){return(0,a.jsx)(i.Suspense,{fallback:(0,a.jsx)("div",{className:"container mx-auto py-6 max-w-4xl",children:(0,a.jsx)("div",{className:"grid place-content-center h-full",children:(0,a.jsx)("p",{className:"font-bold text-xl",children:"جارى التحميل..."})})}),children:(0,a.jsx)(p,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[477,146,688,833,842,441,684,358],()=>s(24595)),_N_E=e.O()}]);