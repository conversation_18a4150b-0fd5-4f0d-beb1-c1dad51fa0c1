{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "sourcesContent": ["import type {\n  AlternateLinkDescriptor,\n  ResolvedAlternateURLs,\n} from '../types/alternative-urls-types'\nimport type {\n  Metadata,\n  ResolvedMetadata,\n  Viewport,\n} from '../types/metadata-interface'\nimport type { ResolvedVerification } from '../types/metadata-types'\nimport type {\n  FieldResolver,\n  FieldResolverExtraArgs,\n  MetadataContext,\n} from '../types/resolvers'\nimport { resolveAsArrayOrUndefined } from '../generate/utils'\nimport { resolveAbsoluteUrlWithPathname } from './resolve-url'\n\nfunction resolveAlternateUrl(\n  url: string | URL,\n  metadataBase: URL | null,\n  metadataContext: MetadataContext\n) {\n  // If alter native url is an URL instance,\n  // we treat it as a URL base and resolve with current pathname\n  if (url instanceof URL) {\n    const newUrl = new URL(metadataContext.pathname, url)\n    url.searchParams.forEach((value, key) =>\n      newUrl.searchParams.set(key, value)\n    )\n    url = newUrl\n  }\n  return resolveAbsoluteUrlWithPathname(url, metadataBase, metadataContext)\n}\n\nexport const resolveThemeColor: FieldResolver<'themeColor', Viewport> = (\n  themeColor\n) => {\n  if (!themeColor) return null\n  const themeColorDescriptors: Viewport['themeColor'] = []\n\n  resolveAsArrayOrUndefined(themeColor)?.forEach((descriptor) => {\n    if (typeof descriptor === 'string')\n      themeColorDescriptors.push({ color: descriptor })\n    else if (typeof descriptor === 'object')\n      themeColorDescriptors.push({\n        color: descriptor.color,\n        media: descriptor.media,\n      })\n  })\n\n  return themeColorDescriptors\n}\n\nfunction resolveUrlValuesOfObject(\n  obj:\n    | Record<\n        string,\n        string | URL | AlternateLinkDescriptor[] | null | undefined\n      >\n    | null\n    | undefined,\n  metadataBase: ResolvedMetadata['metadataBase'],\n  metadataContext: MetadataContext\n): null | Record<string, AlternateLinkDescriptor[]> {\n  if (!obj) return null\n\n  const result: Record<string, AlternateLinkDescriptor[]> = {}\n  for (const [key, value] of Object.entries(obj)) {\n    if (typeof value === 'string' || value instanceof URL) {\n      result[key] = [\n        {\n          url: resolveAlternateUrl(value, metadataBase, metadataContext),\n        },\n      ]\n    } else {\n      result[key] = []\n      value?.forEach((item, index) => {\n        const url = resolveAlternateUrl(item.url, metadataBase, metadataContext)\n        result[key][index] = {\n          url,\n          title: item.title,\n        }\n      })\n    }\n  }\n  return result\n}\n\nfunction resolveCanonicalUrl(\n  urlOrDescriptor: string | URL | null | AlternateLinkDescriptor | undefined,\n  metadataBase: URL | null,\n  metadataContext: MetadataContext\n): null | AlternateLinkDescriptor {\n  if (!urlOrDescriptor) return null\n\n  const url =\n    typeof urlOrDescriptor === 'string' || urlOrDescriptor instanceof URL\n      ? urlOrDescriptor\n      : urlOrDescriptor.url\n\n  // Return string url because structureClone can't handle URL instance\n  return {\n    url: resolveAlternateUrl(url, metadataBase, metadataContext),\n  }\n}\n\nexport const resolveAlternates: FieldResolverExtraArgs<\n  'alternates',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (alternates, metadataBase, context) => {\n  if (!alternates) return null\n\n  const canonical = resolveCanonicalUrl(\n    alternates.canonical,\n    metadataBase,\n    context\n  )\n  const languages = resolveUrlValuesOfObject(\n    alternates.languages,\n    metadataBase,\n    context\n  )\n  const media = resolveUrlValuesOfObject(\n    alternates.media,\n    metadataBase,\n    context\n  )\n  const types = resolveUrlValuesOfObject(\n    alternates.types,\n    metadataBase,\n    context\n  )\n\n  const result: ResolvedAlternateURLs = {\n    canonical,\n    languages,\n    media,\n    types,\n  }\n\n  return result\n}\n\nconst robotsKeys = [\n  'noarchive',\n  'nosnippet',\n  'noimageindex',\n  'nocache',\n  'notranslate',\n  'indexifembedded',\n  'nositelinkssearchbox',\n  'unavailable_after',\n  'max-video-preview',\n  'max-image-preview',\n  'max-snippet',\n] as const\nconst resolveRobotsValue: (robots: Metadata['robots']) => string | null = (\n  robots\n) => {\n  if (!robots) return null\n  if (typeof robots === 'string') return robots\n\n  const values: string[] = []\n\n  if (robots.index) values.push('index')\n  else if (typeof robots.index === 'boolean') values.push('noindex')\n\n  if (robots.follow) values.push('follow')\n  else if (typeof robots.follow === 'boolean') values.push('nofollow')\n\n  for (const key of robotsKeys) {\n    const value = robots[key]\n    if (typeof value !== 'undefined' && value !== false) {\n      values.push(typeof value === 'boolean' ? key : `${key}:${value}`)\n    }\n  }\n\n  return values.join(', ')\n}\n\nexport const resolveRobots: FieldResolver<'robots'> = (robots) => {\n  if (!robots) return null\n  return {\n    basic: resolveRobotsValue(robots),\n    googleBot:\n      typeof robots !== 'string' ? resolveRobotsValue(robots.googleBot) : null,\n  }\n}\n\nconst VerificationKeys = ['google', 'yahoo', 'yandex', 'me', 'other'] as const\nexport const resolveVerification: FieldResolver<'verification'> = (\n  verification\n) => {\n  if (!verification) return null\n  const res: ResolvedVerification = {}\n\n  for (const key of VerificationKeys) {\n    const value = verification[key]\n    if (value) {\n      if (key === 'other') {\n        res.other = {}\n        for (const otherKey in verification.other) {\n          const otherValue = resolveAsArrayOrUndefined(\n            verification.other[otherKey]\n          )\n          if (otherValue) res.other[otherKey] = otherValue\n        }\n      } else res[key] = resolveAsArrayOrUndefined(value) as (string | number)[]\n    }\n  }\n  return res\n}\n\nexport const resolveAppleWebApp: FieldResolver<'appleWebApp'> = (appWebApp) => {\n  if (!appWebApp) return null\n  if (appWebApp === true) {\n    return {\n      capable: true,\n    }\n  }\n\n  const startupImages = appWebApp.startupImage\n    ? resolveAsArrayOrUndefined(appWebApp.startupImage)?.map((item) =>\n        typeof item === 'string' ? { url: item } : item\n      )\n    : null\n\n  return {\n    capable: 'capable' in appWebApp ? !!appWebApp.capable : true,\n    title: appWebApp.title || null,\n    startupImage: startupImages,\n    statusBarStyle: appWebApp.statusBarStyle || 'default',\n  }\n}\n\nexport const resolveAppLinks: FieldResolver<'appLinks'> = (appLinks) => {\n  if (!appLinks) return null\n  for (const key in appLinks) {\n    // @ts-ignore // TODO: type infer\n    appLinks[key] = resolveAsArrayOrUndefined(appLinks[key])\n  }\n  return appLinks as ResolvedMetadata['appLinks']\n}\n\nexport const resolveItunes: FieldResolverExtraArgs<\n  'itunes',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (itunes, metadataBase, context) => {\n  if (!itunes) return null\n  return {\n    appId: itunes.appId,\n    appArgument: itunes.appArgument\n      ? resolveAlternateUrl(itunes.appArgument, metadataBase, context)\n      : undefined,\n  }\n}\n\nexport const resolveFacebook: FieldResolver<'facebook'> = (facebook) => {\n  if (!facebook) return null\n  return {\n    appId: facebook.appId,\n    admins: resolveAsArrayOrUndefined(facebook.admins),\n  }\n}\n\nexport const resolvePagination: FieldResolverExtraArgs<\n  'pagination',\n  [ResolvedMetadata['metadataBase'], MetadataContext]\n> = (pagination, metadataBase, context) => {\n  return {\n    previous: pagination?.previous\n      ? resolveAlternateUrl(pagination.previous, metadataBase, context)\n      : null,\n    next: pagination?.next\n      ? resolveAlternateUrl(pagination.next, metadataBase, context)\n      : null,\n  }\n}\n"], "names": ["resolveAsArrayOrUndefined", "resolveAbsoluteUrlWithPathname", "resolveAlternateUrl", "url", "metadataBase", "metadataContext", "URL", "newUrl", "pathname", "searchParams", "for<PERSON>ach", "value", "key", "set", "resolveThemeColor", "themeColor", "themeColorDescriptors", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "resolveAlternates", "alternates", "context", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "resolveRobots", "basic", "googleBot", "VerificationKeys", "resolveVerification", "verification", "res", "other", "otherKey", "otherValue", "resolveAppleWebApp", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "resolveAppLinks", "appLinks", "resolveItunes", "itunes", "appId", "appArgument", "undefined", "resolveFacebook", "facebook", "admins", "resolvePagination", "pagination", "previous", "next"], "mappings": "AAeA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,8BAA8B,QAAQ,gBAAe;AAE9D,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,eAAgC;IAEhC,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtB,MAAMC,SAAS,IAAID,IAAID,gBAAgBG,QAAQ,EAAEL;QACjDA,IAAIM,YAAY,CAACC,OAAO,CAAC,CAACC,OAAOC,MAC/BL,OAAOE,YAAY,CAACI,GAAG,CAACD,KAAKD;QAE/BR,MAAMI;IACR;IACA,OAAON,+BAA+BE,KAAKC,cAAcC;AAC3D;AAEA,OAAO,MAAMS,oBAA2D,CACtEC;QAKAf;IAHA,IAAI,CAACe,YAAY,OAAO;IACxB,MAAMC,wBAAgD,EAAE;KAExDhB,6BAAAA,0BAA0Be,gCAA1Bf,2BAAuCU,OAAO,CAAC,CAACO;QAC9C,IAAI,OAAOA,eAAe,UACxBD,sBAAsBE,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BD,sBAAsBE,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOJ;AACT,EAAC;AAED,SAASK,yBACPC,GAMa,EACblB,YAA8C,EAC9CC,eAAgC;IAEhC,IAAI,CAACiB,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACX,KAAKD,MAAM,IAAIa,OAAOC,OAAO,CAACH,KAAM;QAC9C,IAAI,OAAOX,UAAU,YAAYA,iBAAiBL,KAAK;YACrDiB,MAAM,CAACX,IAAI,GAAG;gBACZ;oBACET,KAAKD,oBAAoBS,OAAOP,cAAcC;gBAChD;aACD;QACH,OAAO;YACLkB,MAAM,CAACX,IAAI,GAAG,EAAE;YAChBD,yBAAAA,MAAOD,OAAO,CAAC,CAACgB,MAAMC;gBACpB,MAAMxB,MAAMD,oBAAoBwB,KAAKvB,GAAG,EAAEC,cAAcC;gBACxDkB,MAAM,CAACX,IAAI,CAACe,MAAM,GAAG;oBACnBxB;oBACAyB,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOL;AACT;AAEA,SAASM,oBACPC,eAA0E,EAC1E1B,YAAwB,EACxBC,eAAgC;IAEhC,IAAI,CAACyB,iBAAiB,OAAO;IAE7B,MAAM3B,MACJ,OAAO2B,oBAAoB,YAAYA,2BAA2BxB,MAC9DwB,kBACAA,gBAAgB3B,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEA,OAAO,MAAM0B,oBAGT,CAACC,YAAY5B,cAAc6B;IAC7B,IAAI,CAACD,YAAY,OAAO;IAExB,MAAME,YAAYL,oBAChBG,WAAWE,SAAS,EACpB9B,cACA6B;IAEF,MAAME,YAAYd,yBAChBW,WAAWG,SAAS,EACpB/B,cACA6B;IAEF,MAAMb,QAAQC,yBACZW,WAAWZ,KAAK,EAChBhB,cACA6B;IAEF,MAAMG,QAAQf,yBACZW,WAAWI,KAAK,EAChBhC,cACA6B;IAGF,MAAMV,SAAgC;QACpCW;QACAC;QACAf;QACAgB;IACF;IAEA,OAAOb;AACT,EAAC;AAED,MAAMc,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOZ,KAAK,EAAEa,OAAOtB,IAAI,CAAC;SACzB,IAAI,OAAOqB,OAAOZ,KAAK,KAAK,WAAWa,OAAOtB,IAAI,CAAC;IAExD,IAAIqB,OAAOE,MAAM,EAAED,OAAOtB,IAAI,CAAC;SAC1B,IAAI,OAAOqB,OAAOE,MAAM,KAAK,WAAWD,OAAOtB,IAAI,CAAC;IAEzD,KAAK,MAAMN,OAAOyB,WAAY;QAC5B,MAAM1B,QAAQ4B,MAAM,CAAC3B,IAAI;QACzB,IAAI,OAAOD,UAAU,eAAeA,UAAU,OAAO;YACnD6B,OAAOtB,IAAI,CAAC,OAAOP,UAAU,YAAYC,MAAM,GAAGA,IAAI,CAAC,EAAED,OAAO;QAClE;IACF;IAEA,OAAO6B,OAAOE,IAAI,CAAC;AACrB;AAEA,OAAO,MAAMC,gBAAyC,CAACJ;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLK,OAAON,mBAAmBC;QAC1BM,WACE,OAAON,WAAW,WAAWD,mBAAmBC,OAAOM,SAAS,IAAI;IACxE;AACF,EAAC;AAED,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AACrE,OAAO,MAAMC,sBAAqD,CAChEC;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAMrC,OAAOkC,iBAAkB;QAClC,MAAMnC,QAAQqC,YAAY,CAACpC,IAAI;QAC/B,IAAID,OAAO;YACT,IAAIC,QAAQ,SAAS;gBACnBqC,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAapD,0BACjBgD,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAACrC,IAAI,GAAGZ,0BAA0BW;QAC9C;IACF;IACA,OAAOsC;AACT,EAAC;AAED,OAAO,MAAMI,qBAAmD,CAACC;QAS3DtD;IARJ,IAAI,CAACsD,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCzD,6BAAAA,0BAA0BsD,UAAUG,YAAY,sBAAhDzD,2BAAmD0D,GAAG,CAAC,CAAChC,OACtD,OAAOA,SAAS,WAAW;YAAEvB,KAAKuB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACL6B,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxD3B,OAAO0B,UAAU1B,KAAK,IAAI;QAC1B6B,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMjD,OAAOiD,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAACjD,IAAI,GAAGZ,0BAA0B6D,QAAQ,CAACjD,IAAI;IACzD;IACA,OAAOiD;AACT,EAAC;AAED,OAAO,MAAMC,gBAGT,CAACC,QAAQ3D,cAAc6B;IACzB,IAAI,CAAC8B,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B/D,oBAAoB6D,OAAOE,WAAW,EAAE7D,cAAc6B,WACtDiC;IACN;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,OAAO;QACLJ,OAAOI,SAASJ,KAAK;QACrBK,QAAQrE,0BAA0BoE,SAASC,MAAM;IACnD;AACF,EAAC;AAED,OAAO,MAAMC,oBAGT,CAACC,YAAYnE,cAAc6B;IAC7B,OAAO;QACLuC,UAAUD,CAAAA,8BAAAA,WAAYC,QAAQ,IAC1BtE,oBAAoBqE,WAAWC,QAAQ,EAAEpE,cAAc6B,WACvD;QACJwC,MAAMF,CAAAA,8BAAAA,WAAYE,IAAI,IAClBvE,oBAAoBqE,WAAWE,IAAI,EAAErE,cAAc6B,WACnD;IACN;AACF,EAAC"}