{"version": 3, "sources": ["../../../../src/server/app-render/rsc/preloads.ts"], "sourcesContent": ["/*\n\nFiles in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.\n\n*/\n\nimport ReactDOM from 'react-dom'\n\nexport function preloadStyle(\n  href: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = { as: 'style' }\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ReactDOM.preload(href, opts)\n}\n\nexport function preloadFont(\n  href: string,\n  type: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = { as: 'font', type }\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ReactDOM.preload(href, opts)\n}\n\nexport function preconnect(\n  href: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = {}\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ;(ReactDOM as any).preconnect(href, opts)\n}\n"], "names": ["preconnect", "preloadFont", "preloadStyle", "href", "crossOrigin", "nonce", "opts", "as", "ReactDOM", "preload", "type"], "mappings": "AAAA;;;;AAIA;;;;;;;;;;;;;;;;IAmCgBA,UAAU;eAAVA;;IAhBAC,WAAW;eAAXA;;IAfAC,YAAY;eAAZA;;;iEAFK;;;;;;AAEd,SAASA,aACdC,IAAY,EACZC,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY;QAAEC,IAAI;IAAQ;IAChC,IAAI,OAAOH,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;IACAG,iBAAQ,CAACC,OAAO,CAACN,MAAMG;AACzB;AAEO,SAASL,YACdE,IAAY,EACZO,IAAY,EACZN,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY;QAAEC,IAAI;QAAQG;IAAK;IACrC,IAAI,OAAON,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;IACAG,iBAAQ,CAACC,OAAO,CAACN,MAAMG;AACzB;AAEO,SAASN,WACdG,IAAY,EACZC,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY,CAAC;IACnB,IAAI,OAAOF,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;;IACEG,iBAAQ,CAASR,UAAU,CAACG,MAAMG;AACtC"}