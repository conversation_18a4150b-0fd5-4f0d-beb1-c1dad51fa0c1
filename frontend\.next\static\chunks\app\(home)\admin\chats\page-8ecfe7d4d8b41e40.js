(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[517],{27290:(e,r,a)=>{"use strict";a.d(r,{Z:()=>j});var s=a(65262),t=a(69478),l=a(66232),n=(0,t.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...l.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),i=a(12115),o=a(73750),d=a(81627),c=a(77151),u=a(9906),b=a(88629),f=a(75894),h=a(56973),m=a(5712),v=a(81467),g=a(672),p=a(491),x=a(6548),y=a(35925),k=a(9539),w=a(95155),N=(0,h.Rf)((e,r)=>{let{children:a,context:t,Component:l,isPressable:N,disableAnimation:j,disableRipple:C,getCardProps:D,getRippleProps:E}=function(e){var r,a,s,t;let l=(0,f.o)(),[k,w]=(0,h.rE)(e,n.variantKeys),{ref:N,as:j,children:C,onClick:D,onPress:E,autoFocus:P,className:B,classNames:A,allowTextSelectionOnPress:z=!0,...L}=k,T=(0,x.zD)(N),H=j||(e.isPressable?"button":"div"),M="string"==typeof H,R=null!=(a=null!=(r=e.disableAnimation)?r:null==l?void 0:l.disableAnimation)&&a,W=null!=(t=null!=(s=e.disableRipple)?s:null==l?void 0:l.disableRipple)&&t,$=(0,m.$)(null==A?void 0:A.base,B),{onClear:_,onPress:U,ripples:F}=(0,y.k)(),I=(0,i.useCallback)(e=>{W||R||T.current&&U(e)},[W,R,T,U]),{buttonProps:S,isPressed:O}=(0,b.l)({onPress:(0,o.c)(E,I),elementType:j,isDisabled:!e.isPressable,onClick:D,allowTextSelectionOnPress:z,...L},T),{hoverProps:V,isHovered:G}=(0,u.M)({isDisabled:!e.isHoverable,...L}),{isFocusVisible:Z,isFocused:K,focusProps:q}=(0,c.o)({autoFocus:P}),J=(0,i.useMemo)(()=>n({...w,disableAnimation:R}),[(0,v.t6)(w),R]),Q=(0,i.useMemo)(()=>({slots:J,classNames:A,disableAnimation:R,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[J,A,e.isDisabled,e.isFooterBlurred,R,e.fullWidth]),X=(0,i.useCallback)(function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:T,className:J.base({class:$}),tabIndex:e.isPressable?0:-1,"data-hover":(0,g.sE)(G),"data-pressed":(0,g.sE)(O),"data-focus":(0,g.sE)(K),"data-focus-visible":(0,g.sE)(Z),"data-disabled":(0,g.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{...S,...q,role:"button"}:{},e.isHoverable?V:{},(0,p.$)(L,{enabled:M}),(0,p.$)(r))}},[T,J,$,M,e.isPressable,e.isHoverable,e.isDisabled,G,O,Z,S,q,V,L]),Y=(0,i.useCallback)(()=>({ripples:F,onClear:_}),[F,_]);return{context:Q,domRef:T,Component:H,classNames:A,children:C,isHovered:G,isPressed:O,disableAnimation:R,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:W,handlePress:I,isFocusVisible:Z,getCardProps:X,getRippleProps:Y}}({...e,ref:r});return(0,w.jsxs)(l,{...D(),children:[(0,w.jsx)(s.u,{value:t,children:a}),N&&!j&&!C&&(0,w.jsx)(k.j,{...E()})]})});N.displayName="NextUI.Card";var j=N},33777:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m,dynamic:()=>h});var s=a(95155),t=a(12115);let l=(0,a(40157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var n=a(27290),i=a(66575),o=a(54736),d=a(81495),c=a(66146),u=a(56671),b=a(59434),f=a(82842);let h="force-dynamic";function m(){let[e,r]=(0,t.useState)([]),[a,h]=(0,t.useState)(!0),[m]=(0,f.lT)(),v=(0,t.useCallback)(async e=>{h(!0);let a=await (0,b.G)(e,null,"GET",m.access);if(200===a.status){let e=await a.json();console.log(e),r(e.results),h(!1)}else h(!1),u.o.error("فشل تحميل الغرف، برجاء اعادة المحاولة")},[m]);return(0,t.useEffect)(()=>{v("/chats/")},[v]),(0,s.jsx)("div",{className:"container mx-auto py-10",children:(0,s.jsxs)(n.Z,{className:"border-none shadow-md",children:[(0,s.jsx)(i.d,{className:"bg-gray-50 dark:bg-gray-800 rounded-t-lg font-bold text-3xl",children:"رسائل العملاء"}),(0,s.jsx)(o.U,{className:"p-6",children:a?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)(l,{className:"h-8 w-8 animate-spin text-gray-400"})}):(0,s.jsxs)("div",{className:"border rounded-md overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 border-b",children:(0,s.jsxs)("div",{className:"grid grid-cols-12 px-4 py-3 text-sm font-medium text-gray-500 dark:text-gray-400",children:[(0,s.jsx)("div",{className:"col-span-2",children:"الرقم التعريفى"}),(0,s.jsx)("div",{className:"col-span-6 text-center mr-[20rem]",children:"اسم الغرفة"}),(0,s.jsx)("div",{className:"col-span-2 text-left",children:"الاجراءات"})]})}),(0,s.jsx)("div",{className:"divide-y",children:e.map(e=>(0,s.jsxs)("div",{className:"grid grid-cols-12 px-4 py-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors",children:[(0,s.jsx)("div",{className:"col-span-2 font-medium",children:e.id}),(0,s.jsxs)("div",{className:"col-span-8 text-center",children:["الغرفة ",e.room]}),(0,s.jsx)("div",{className:"col-span-2",children:(0,s.jsx)(c.T,{href:"/admin/chats/".concat(e.id,"?room=").concat(e.room),as:d.h,size:"sm",children:"عرض"})})]},e.id))})]})})]})})}},40157:(e,r,a)=>{"use strict";a.d(r,{A:()=>o});var s=a(12115);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return r.filter((e,r,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===r).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)((e,r)=>{let{color:a="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...b}=e;return(0,s.createElement)("svg",{ref:r,...n,width:t,height:t,stroke:a,strokeWidth:o?24*Number(i)/Number(t):i,className:l("lucide",d),...b},[...u.map(e=>{let[r,a]=e;return(0,s.createElement)(r,a)}),...Array.isArray(c)?c:[c]])}),o=(e,r)=>{let a=(0,s.forwardRef)((a,n)=>{let{className:o,...d}=a;return(0,s.createElement)(i,{ref:n,iconNode:r,className:l("lucide-".concat(t(e)),o),...d})});return a.displayName="".concat(e),a}},46900:(e,r,a)=>{Promise.resolve().then(a.bind(a,33777))},54736:(e,r,a)=>{"use strict";a.d(r,{U:()=>d});var s=a(65262),t=a(56973),l=a(6548),n=a(5712),i=a(95155),o=(0,t.Rf)((e,r)=>{var a;let{as:t,className:o,children:d,...c}=e,u=(0,l.zD)(r),{slots:b,classNames:f}=(0,s.f)(),h=(0,n.$)(null==f?void 0:f.body,o);return(0,i.jsx)(t||"div",{ref:u,className:null==(a=b.body)?void 0:a.call(b,{class:h}),...c,children:d})});o.displayName="NextUI.CardBody";var d=o},59434:(e,r,a)=>{"use strict";a.d(r,{G:()=>n,cn:()=>l,f:()=>i});var s=a(52596),t=a(39688);function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,t.QP)((0,s.$)(r))}async function n(e,r){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",s=arguments.length>3?arguments[3]:void 0;return await fetch("http://localhost:8000"+e,{method:a,body:null===r?null:JSON.stringify(r),headers:{"Content-Type":"application/json",Authorization:s?"Bearer ".concat(s):""}})}let i=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},65262:(e,r,a)=>{"use strict";a.d(r,{f:()=>t,u:()=>s});var[s,t]=(0,a(42810).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},66575:(e,r,a)=>{"use strict";a.d(r,{d:()=>d});var s=a(65262),t=a(56973),l=a(6548),n=a(5712),i=a(95155),o=(0,t.Rf)((e,r)=>{var a;let{as:t,className:o,children:d,...c}=e,u=(0,l.zD)(r),{slots:b,classNames:f}=(0,s.f)(),h=(0,n.$)(null==f?void 0:f.header,o);return(0,i.jsx)(t||"div",{ref:u,className:null==(a=b.header)?void 0:a.call(b,{class:h}),...c,children:d})});o.displayName="NextUI.CardHeader";var d=o},81495:(e,r,a)=>{"use strict";a.d(r,{h:()=>N});var s=a(69478),t=a(66232),l=(0,s.tv)({base:["relative inline-flex items-center outline-none tap-highlight-transparent",...t.zb],variants:{size:{sm:"text-small",md:"text-medium",lg:"text-large"},color:{foreground:"text-foreground",primary:"text-primary",secondary:"text-secondary",success:"text-success",warning:"text-warning",danger:"text-danger"},underline:{none:"no-underline",hover:"hover:underline",always:"underline",active:"active:underline",focus:"focus:underline"},isBlock:{true:["px-2","py-1","hover:after:opacity-100","after:content-['']","after:inset-0","after:opacity-0","after:w-full","after:h-full","after:rounded-xl","after:transition-background","after:absolute"],false:"hover:opacity-80 active:opacity-disabled transition-opacity"},isDisabled:{true:"opacity-disabled cursor-default pointer-events-none"},disableAnimation:{true:"after:transition-none transition-none"}},compoundVariants:[{isBlock:!0,color:"foreground",class:"hover:after:bg-foreground/10"},{isBlock:!0,color:"primary",class:"hover:after:bg-primary/20"},{isBlock:!0,color:"secondary",class:"hover:after:bg-secondary/20"},{isBlock:!0,color:"success",class:"hover:after:bg-success/20"},{isBlock:!0,color:"warning",class:"hover:after:bg-warning/20"},{isBlock:!0,color:"danger",class:"hover:after:bg-danger/20"},{underline:["hover","always","active","focus"],class:"underline-offset-4"}],defaultVariants:{color:"primary",size:"md",isBlock:!1,underline:"none",isDisabled:!1}}),n=a(66680),i=a(78257),o=a(81627),d=a(22989),c=a(86176),u=a(71071),b=a(19914),f=a(75894),h=a(56973),m=a(6548),v=a(77151),g=a(81467),p=a(672),x=a(12115),y=a(95155),k=e=>(0,y.jsxs)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",shapeRendering:"geometricPrecision",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[(0,y.jsx)("path",{d:"M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6"}),(0,y.jsx)("path",{d:"M15 3h6v6"}),(0,y.jsx)("path",{d:"M10 14L21 3"})]}),w=(0,h.Rf)((e,r)=>{let{Component:a,children:s,showAnchorIcon:t,anchorIcon:w=(0,y.jsx)(k,{className:"flex mx-1 text-current self-center"}),getLinkProps:N}=function(e){var r,a,s,t;let y=(0,f.o)(),[k,w]=(0,h.rE)(e,l.variantKeys),{ref:N,as:j,children:C,anchorIcon:D,isExternal:E=!1,showAnchorIcon:P=!1,autoFocus:B=!1,className:A,onPress:z,onPressStart:L,onPressEnd:T,onClick:H,...M}=k,R=(0,m.zD)(N),W=null!=(a=null!=(r=null==e?void 0:e.disableAnimation)?r:null==y?void 0:y.disableAnimation)&&a,{linkProps:$}=function(e,r){let{elementType:a="a",onPress:s,onPressStart:t,onPressEnd:l,onClick:f,role:h,isDisabled:m,...v}=e,g={};"a"!==a&&(g={role:"link",tabIndex:m?void 0:0});let p=(0,n.un)()||(0,n.m0)();f&&"function"==typeof f&&"button"!==h&&(0,c.R)("onClick is deprecated, please use onPress instead. See: https://github.com/nextui-org/nextui/issues/4292","useLink");let{focusableProps:x}=(0,u.W)(e,r),{pressProps:y,isPressed:k}=(0,b.d)({onPress:e=>{p&&(null==f||f(e)),null==s||s(e)},onPressStart:t,onPressEnd:l,isDisabled:m,ref:r}),w=(0,i.$)(v,{labelable:!0,isLink:"a"===a}),N=(0,o.v)(x,y),j=(0,d.rd)(),C=(0,d._h)(e);return{isPressed:k,linkProps:(0,o.v)(w,C,{...N,...g,"aria-disabled":m||void 0,"aria-current":e["aria-current"],onClick:r=>{var a;null==(a=y.onClick)||a.call(y,r),!p&&f&&f(r),!j.isNative&&r.currentTarget instanceof HTMLAnchorElement&&r.currentTarget.href&&!r.isDefaultPrevented()&&(0,d.sU)(r.currentTarget,r)&&e.href&&(r.preventDefault(),j.open(r.currentTarget,r,e.href,e.routerOptions))}})}}({...M,onPress:z,onPressStart:L,onPressEnd:T,onClick:H,isDisabled:e.isDisabled,elementType:"".concat(j)},R),{isFocused:_,isFocusVisible:U,focusProps:F}=(0,v.o)({autoFocus:B});E&&(M.rel=null!=(s=M.rel)?s:"noopener noreferrer",M.target=null!=(t=M.target)?t:"_blank");let I=(0,x.useMemo)(()=>l({...w,disableAnimation:W,className:A}),[(0,g.t6)(w),W,A]);return{Component:j||"a",children:C,anchorIcon:D,showAnchorIcon:P,getLinkProps:(0,x.useCallback)(()=>({ref:R,className:I,"data-focus":(0,p.sE)(_),"data-disabled":(0,p.sE)(e.isDisabled),"data-focus-visible":(0,p.sE)(U),...(0,o.v)(F,$,M)}),[I,_,U,F,$,M])}}({ref:r,...e});return(0,y.jsx)(a,{...N(),children:(0,y.jsxs)(y.Fragment,{children:[s,t&&w]})})});w.displayName="NextUI.Link";var N=w}},e=>{var r=r=>e(e.s=r);e.O(0,[477,146,688,671,842,441,684,358],()=>r(46900)),_N_E=e.O()}]);