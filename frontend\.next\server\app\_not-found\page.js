(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42873:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});var n=t(60687),o=t(73705);function s({children:e}){return(0,n.jsx)(o.b,{children:e})}t(43210)},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64409:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var n=t(65239),o=t(48088),s=t(88170),i=t.n(s),d=t(30893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],u={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},71937:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},85089:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},89707:(e,r,t)=>{Promise.resolve().then(t.bind(t,52581)),Promise.resolve().then(t.bind(t,42873))},89987:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\ClientProviders.tsx","default")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>a});var n=t(37413),o=t(98856),s=t.n(o);t(61135);var i=t(89987),d=t(6931);let a={title:"Palastine Emergency",description:"Comming for help when you need us"};function l({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsxs)("body",{suppressHydrationWarning:!0,className:`${s().variable} antialiased`,children:[(0,n.jsx)(i.default,{children:e}),(0,n.jsx)(d.Toaster,{richColors:!0,position:"top-right"})]})})}},95283:(e,r,t)=>{Promise.resolve().then(t.bind(t,6931)),Promise.resolve().then(t.bind(t,89987))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,310],()=>t(64409));module.exports=n})();