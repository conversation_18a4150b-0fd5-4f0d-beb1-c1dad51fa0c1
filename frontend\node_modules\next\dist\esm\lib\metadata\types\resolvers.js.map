{"version": 3, "sources": ["../../../../src/lib/metadata/types/resolvers.ts"], "sourcesContent": ["import type { Metadata, ResolvedMetadata } from './metadata-interface'\n\nexport type FieldResolver<\n  Key extends keyof Data & keyof ResolvedData,\n  Data = Metadata,\n  ResolvedData = ResolvedMetadata,\n> = (T: Data[Key]) => ResolvedData[Key]\n\nexport type FieldResolverExtraArgs<\n  Key extends keyof Data & keyof ResolvedData,\n  ExtraArgs extends unknown[] = any[],\n  Data = Metadata,\n  ResolvedData = ResolvedMetadata,\n> = (T: Data[Key], ...args: ExtraArgs) => ResolvedData[Key]\n\nexport type MetadataContext = {\n  pathname: string\n  trailingSlash: boolean\n  isStaticMetadataRouteFile: boolean\n}\n"], "names": [], "mappings": "AAeA,WAIC"}