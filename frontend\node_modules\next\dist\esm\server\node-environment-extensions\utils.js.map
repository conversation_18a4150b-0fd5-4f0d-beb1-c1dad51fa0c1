{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender') {\n      const prerenderSignal = workUnitStore.controller.signal\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct any stacks\n        // because something else actually terminated the prerender.\n        const workStore = workAsyncStorage.getStore()\n        if (workStore) {\n          let message: string\n          switch (type) {\n            case 'time':\n              message = `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n              break\n            case 'random':\n              message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n              break\n            case 'crypto':\n              message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n              break\n            default:\n              throw new InvariantError(\n                'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n              )\n          }\n          const errorWithStack = new Error(message)\n\n          abortOnSynchronousPlatformIOAccess(\n            workStore.route,\n            expression,\n            errorWithStack,\n            workUnitStore\n          )\n        }\n      }\n    } else if (\n      workUnitStore.type === 'request' &&\n      workUnitStore.prerenderPhase === true\n    ) {\n      const requestStore = workUnitStore\n      trackSynchronousPlatformIOAccessInDev(requestStore)\n    }\n  }\n}\n"], "names": ["workAsyncStorage", "workUnitAsyncStorage", "abortOnSynchronousPlatformIOAccess", "trackSynchronousPlatformIOAccessInDev", "InvariantError", "io", "expression", "type", "workUnitStore", "getStore", "prerenderSignal", "controller", "signal", "aborted", "workStore", "message", "route", "errorWithStack", "Error", "prerenderPhase", "requestStore"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SACEC,kCAAkC,EAClCC,qCAAqC,QAChC,kCAAiC;AACxC,SAASC,cAAc,QAAQ,mCAAkC;AAIjE,OAAO,SAASC,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBP,qBAAqBQ,QAAQ;IACnD,IAAID,eAAe;QACjB,IAAIA,cAAcD,IAAI,KAAK,aAAa;YACtC,MAAMG,kBAAkBF,cAAcG,UAAU,CAACC,MAAM;YACvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;gBACrC,mFAAmF;gBACnF,4DAA4D;gBAC5D,MAAMC,YAAYd,iBAAiBS,QAAQ;gBAC3C,IAAIK,WAAW;oBACb,IAAIC;oBACJ,OAAQR;wBACN,KAAK;4BACHQ,UAAU,CAAC,OAAO,EAAED,UAAUE,KAAK,CAAC,OAAO,EAAEV,WAAW,mLAAmL,CAAC;4BAC5O;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAED,UAAUE,KAAK,CAAC,OAAO,EAAEV,WAAW,wKAAwK,CAAC;4BACjO;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAED,UAAUE,KAAK,CAAC,OAAO,EAAEV,WAAW,wKAAwK,CAAC;4BACjO;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIF,eACR,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBACA,MAAMa,iBAAiB,qBAAkB,CAAlB,IAAIC,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAExCb,mCACEY,UAAUE,KAAK,EACfV,YACAW,gBACAT;gBAEJ;YACF;QACF,OAAO,IACLA,cAAcD,IAAI,KAAK,aACvBC,cAAcW,cAAc,KAAK,MACjC;YACA,MAAMC,eAAeZ;YACrBL,sCAAsCiB;QACxC;IACF;AACF"}