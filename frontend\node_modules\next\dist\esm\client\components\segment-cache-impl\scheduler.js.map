{"version": 3, "sources": ["../../../../src/client/components/segment-cache-impl/scheduler.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  Segment as FlightRouterStateSegment,\n} from '../../../server/app-render/types'\nimport { matchSegment } from '../match-segments'\nimport {\n  readOrCreateRouteCacheEntry,\n  readOrCreateSegmentCacheEntry,\n  fetchRouteOnCacheMiss,\n  fetchSegmentOnCacheMiss,\n  EntryStatus,\n  type FulfilledRouteCacheEntry,\n  type RouteCacheEntry,\n  type SegmentCacheEntry,\n  type RouteTree,\n  fetchSegmentPrefetchesUsingDynamicRequest,\n  type PendingSegmentCacheEntry,\n  convertRouteTreeToFlightRouterState,\n  FetchStrategy,\n  readOrCreateRevalidatingSegmentEntry,\n  upsertSegmentEntry,\n  type FulfilledSegmentCacheEntry,\n  upgradeToPendingSegment,\n  waitForSegmentCacheEntry,\n  resetRevalidatingSegmentEntry,\n  getSegmentKeypathForTask,\n} from './cache'\nimport type { RouteCacheKey } from './cache-key'\nimport { PrefetchPriority } from '../segment-cache'\n\nconst scheduleMicrotask =\n  typeof queueMicrotask === 'function'\n    ? queueMicrotask\n    : (fn: () => unknown) =>\n        Promise.resolve()\n          .then(fn)\n          .catch((error) =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n\nexport type PrefetchTask = {\n  key: RouteCacheKey\n\n  /**\n   * The FlightRouterState at the time the task was initiated. This is needed\n   * when falling back to the non-PPR behavior, which only prefetches up to\n   * the first loading boundary.\n   */\n  treeAtTimeOfPrefetch: FlightRouterState\n\n  /**\n   * Whether to prefetch dynamic data, in addition to static data. This is\n   * used by <Link prefetch={true}>.\n   */\n  includeDynamicData: boolean\n\n  /**\n   * sortId is an incrementing counter\n   *\n   * Newer prefetches are prioritized over older ones, so that as new links\n   * enter the viewport, they are not starved by older links that are no\n   * longer relevant. In the future, we can add additional prioritization\n   * heuristics, like removing prefetches once a link leaves the viewport.\n   *\n   * The sortId is assigned when the prefetch is initiated, and reassigned if\n   * the same task is prefetched again (effectively bumping it to the top of\n   * the queue).\n   *\n   * TODO: We can add additional fields here to indicate what kind of prefetch\n   * it is. For example, was it initiated by a link? Or was it an imperative\n   * call? If it was initiated by a link, we can remove it from the queue when\n   * the link leaves the viewport, but if it was an imperative call, then we\n   * should keep it in the queue until it's fulfilled.\n   *\n   * We can also add priority levels. For example, hovering over a link could\n   * increase the priority of its prefetch.\n   */\n  sortId: number\n\n  /**\n   * The priority of the task. Like sortId, this affects the task's position in\n   * the queue, so it must never be updated without resifting the heap.\n   */\n  priority: PrefetchPriority\n\n  /**\n   * The phase of the task. Tasks are split into multiple phases so that their\n   * priority can be adjusted based on what kind of work they're doing.\n   * Concretely, prefetching the route tree is higher priority than prefetching\n   * segment data.\n   */\n  phase: PrefetchPhase\n\n  /**\n   * Temporary state for tracking the currently running task. This is currently\n   * used to track whether a task deferred some work to run background at\n   * priority, but we might need it for additional state in the future.\n   */\n  hasBackgroundWork: boolean\n\n  /**\n   * True if the prefetch was cancelled.\n   */\n  isCanceled: boolean\n\n  /**\n   * The index of the task in the heap's backing array. Used to efficiently\n   * change the priority of a task by re-sifting it, which requires knowing\n   * where it is in the array. This is only used internally by the heap\n   * algorithm. The naive alternative is indexOf every time a task is queued,\n   * which has O(n) complexity.\n   *\n   * We also use this field to check whether a task is currently in the queue.\n   */\n  _heapIndex: number\n}\n\nconst enum PrefetchTaskExitStatus {\n  /**\n   * The task yielded because there are too many requests in progress.\n   */\n  InProgress,\n\n  /**\n   * The task is blocked. It needs more data before it can proceed.\n   *\n   * Currently the only reason this happens is we're still waiting to receive a\n   * route tree from the server, because we can't start prefetching the segments\n   * until we know what to prefetch.\n   */\n  Blocked,\n\n  /**\n   * There's nothing left to prefetch.\n   */\n  Done,\n}\n\n/**\n * Prefetch tasks are processed in two phases: first the route tree is fetched,\n * then the segments. We use this to priortize tasks that have not yet fetched\n * the route tree.\n */\nconst enum PrefetchPhase {\n  RouteTree = 1,\n  Segments = 0,\n}\n\nexport type PrefetchSubtaskResult<T> = {\n  /**\n   * A promise that resolves when the network connection is closed.\n   */\n  closed: Promise<void>\n  value: T\n}\n\nconst taskHeap: Array<PrefetchTask> = []\n\n// This is intentionally low so that when a navigation happens, the browser's\n// internal network queue is not already saturated with prefetch requests.\nconst MAX_CONCURRENT_PREFETCH_REQUESTS = 3\nlet inProgressRequests = 0\n\nlet sortIdCounter = 0\nlet didScheduleMicrotask = false\n\n/**\n * Initiates a prefetch task for the given URL. If a prefetch for the same URL\n * is already in progress, this will bump it to the top of the queue.\n *\n * This is not a user-facing function. By the time this is called, the href is\n * expected to be validated and normalized.\n *\n * @param key The RouteCacheKey to prefetch.\n * @param treeAtTimeOfPrefetch The app's current FlightRouterState\n * @param includeDynamicData Whether to prefetch dynamic data, in addition to\n * static data. This is used by <Link prefetch={true}>.\n */\nexport function schedulePrefetchTask(\n  key: RouteCacheKey,\n  treeAtTimeOfPrefetch: FlightRouterState,\n  includeDynamicData: boolean,\n  priority: PrefetchPriority\n): PrefetchTask {\n  // Spawn a new prefetch task\n  const task: PrefetchTask = {\n    key,\n    treeAtTimeOfPrefetch,\n    priority,\n    phase: PrefetchPhase.RouteTree,\n    hasBackgroundWork: false,\n    includeDynamicData,\n    sortId: sortIdCounter++,\n    isCanceled: false,\n    _heapIndex: -1,\n  }\n  heapPush(taskHeap, task)\n\n  // Schedule an async task to process the queue.\n  //\n  // The main reason we process the queue in an async task is for batching.\n  // It's common for a single JS task/event to trigger multiple prefetches.\n  // By deferring to a microtask, we only process the queue once per JS task.\n  // If they have different priorities, it also ensures they are processed in\n  // the optimal order.\n  ensureWorkIsScheduled()\n\n  return task\n}\n\nexport function cancelPrefetchTask(task: PrefetchTask): void {\n  // Remove the prefetch task from the queue. If the task already completed,\n  // then this is a no-op.\n  //\n  // We must also explicitly mark the task as canceled so that a blocked task\n  // does not get added back to the queue when it's pinged by the network.\n  task.isCanceled = true\n  heapDelete(taskHeap, task)\n}\n\nexport function reschedulePrefetchTask(\n  task: PrefetchTask,\n  treeAtTimeOfPrefetch: FlightRouterState,\n  includeDynamicData: boolean,\n  priority: PrefetchPriority\n): void {\n  // Bump the prefetch task to the top of the queue, as if it were a fresh\n  // task. This is essentially the same as canceling the task and scheduling\n  // a new one, except it reuses the original object.\n  //\n  // The primary use case is to increase the priority of a Link-initated\n  // prefetch on hover.\n\n  // Un-cancel the task, in case it was previously canceled.\n  task.isCanceled = false\n  task.phase = PrefetchPhase.RouteTree\n\n  // Assign a new sort ID to move it ahead of all other tasks at the same\n  // priority level. (Higher sort IDs are processed first.)\n  task.sortId = sortIdCounter++\n  task.priority = priority\n\n  task.treeAtTimeOfPrefetch = treeAtTimeOfPrefetch\n  task.includeDynamicData = includeDynamicData\n\n  if (task._heapIndex !== -1) {\n    // The task is already in the queue.\n    heapResift(taskHeap, task)\n  } else {\n    heapPush(taskHeap, task)\n  }\n  ensureWorkIsScheduled()\n}\n\nfunction ensureWorkIsScheduled() {\n  if (didScheduleMicrotask || !hasNetworkBandwidth()) {\n    // Either we already scheduled a task to process the queue, or there are\n    // too many concurrent requests in progress. In the latter case, the\n    // queue will resume processing once more bandwidth is available.\n    return\n  }\n  didScheduleMicrotask = true\n  scheduleMicrotask(processQueueInMicrotask)\n}\n\n/**\n * Checks if we've exceeded the maximum number of concurrent prefetch requests,\n * to avoid saturating the browser's internal network queue. This is a\n * cooperative limit — prefetch tasks should check this before issuing\n * new requests.\n */\nfunction hasNetworkBandwidth(): boolean {\n  // TODO: Also check if there's an in-progress navigation. We should never\n  // add prefetch requests to the network queue if an actual navigation is\n  // taking place, to ensure there's sufficient bandwidth for render-blocking\n  // data and resources.\n  return inProgressRequests < MAX_CONCURRENT_PREFETCH_REQUESTS\n}\n\nfunction spawnPrefetchSubtask<T>(\n  prefetchSubtask: Promise<PrefetchSubtaskResult<T> | null>\n): Promise<T | null> {\n  // When the scheduler spawns an async task, we don't await its result.\n  // Instead, the async task writes its result directly into the cache, then\n  // pings the scheduler to continue.\n  //\n  // We process server responses streamingly, so the prefetch subtask will\n  // likely resolve before we're finished receiving all the data. The subtask\n  // result includes a promise that resolves once the network connection is\n  // closed. The scheduler uses this to control network bandwidth by tracking\n  // and limiting the number of concurrent requests.\n  inProgressRequests++\n  return prefetchSubtask.then((result) => {\n    if (result === null) {\n      // The prefetch task errored before it could start processing the\n      // network stream. Assume the connection is closed.\n      onPrefetchConnectionClosed()\n      return null\n    }\n    // Wait for the connection to close before freeing up more bandwidth.\n    result.closed.then(onPrefetchConnectionClosed)\n    return result.value\n  })\n}\n\nfunction onPrefetchConnectionClosed(): void {\n  inProgressRequests--\n\n  // Notify the scheduler that we have more bandwidth, and can continue\n  // processing tasks.\n  ensureWorkIsScheduled()\n}\n\n/**\n * Notify the scheduler that we've received new data for an in-progress\n * prefetch. The corresponding task will be added back to the queue (unless the\n * task has been canceled in the meantime).\n */\nexport function pingPrefetchTask(task: PrefetchTask) {\n  // \"Ping\" a prefetch that's already in progress to notify it of new data.\n  if (\n    // Check if prefetch was canceled.\n    task.isCanceled ||\n    // Check if prefetch is already queued.\n    task._heapIndex !== -1\n  ) {\n    return\n  }\n  // Add the task back to the queue.\n  heapPush(taskHeap, task)\n  ensureWorkIsScheduled()\n}\n\nfunction processQueueInMicrotask() {\n  didScheduleMicrotask = false\n\n  // We aim to minimize how often we read the current time. Since nearly all\n  // functions in the prefetch scheduler are synchronous, we can read the time\n  // once and pass it as an argument wherever it's needed.\n  const now = Date.now()\n\n  // Process the task queue until we run out of network bandwidth.\n  let task = heapPeek(taskHeap)\n  while (task !== null && hasNetworkBandwidth()) {\n    const route = readOrCreateRouteCacheEntry(now, task)\n    const exitStatus = pingRootRouteTree(now, task, route)\n\n    // The `hasBackgroundWork` field is only valid for a single attempt. Reset\n    // it immediately upon exit.\n    const hasBackgroundWork = task.hasBackgroundWork\n    task.hasBackgroundWork = false\n\n    switch (exitStatus) {\n      case PrefetchTaskExitStatus.InProgress:\n        // The task yielded because there are too many requests in progress.\n        // Stop processing tasks until we have more bandwidth.\n        return\n      case PrefetchTaskExitStatus.Blocked:\n        // The task is blocked. It needs more data before it can proceed.\n        // Keep the task out of the queue until the server responds.\n        heapPop(taskHeap)\n        // Continue to the next task\n        task = heapPeek(taskHeap)\n        continue\n      case PrefetchTaskExitStatus.Done:\n        if (task.phase === PrefetchPhase.RouteTree) {\n          // Finished prefetching the route tree. Proceed to prefetching\n          // the segments.\n          task.phase = PrefetchPhase.Segments\n          heapResift(taskHeap, task)\n        } else if (hasBackgroundWork) {\n          // The task spawned additional background work. Reschedule the task\n          // at background priority.\n          task.priority = PrefetchPriority.Background\n          heapResift(taskHeap, task)\n        } else {\n          // The prefetch is complete. Continue to the next task.\n          heapPop(taskHeap)\n        }\n        task = heapPeek(taskHeap)\n        continue\n      default:\n        exitStatus satisfies never\n    }\n  }\n}\n\n/**\n * Check this during a prefetch task to determine if background work can be\n * performed. If so, it evaluates to `true`. Otherwise, it returns `false`,\n * while also scheduling a background task to run later. Usage:\n *\n * @example\n * if (background(task)) {\n *   // Perform background-pri work\n * }\n */\nfunction background(task: PrefetchTask): boolean {\n  if (task.priority === PrefetchPriority.Background) {\n    return true\n  }\n  task.hasBackgroundWork = true\n  return false\n}\n\nfunction pingRootRouteTree(\n  now: number,\n  task: PrefetchTask,\n  route: RouteCacheEntry\n): PrefetchTaskExitStatus {\n  switch (route.status) {\n    case EntryStatus.Empty: {\n      // Route is not yet cached, and there's no request already in progress.\n      // Spawn a task to request the route, load it into the cache, and ping\n      // the task to continue.\n\n      // TODO: There are multiple strategies in the <Link> API for prefetching\n      // a route. Currently we've only implemented the main one: per-segment,\n      // static-data only.\n      //\n      // There's also <Link prefetch={true}> which prefetches both static *and*\n      // dynamic data. Similarly, we need to fallback to the old, per-page\n      // behavior if PPR is disabled for a route (via the incremental opt-in).\n      //\n      // Those cases will be handled here.\n      spawnPrefetchSubtask(fetchRouteOnCacheMiss(route, task))\n\n      // If the request takes longer than a minute, a subsequent request should\n      // retry instead of waiting for this one. When the response is received,\n      // this value will be replaced by a new value based on the stale time sent\n      // from the server.\n      // TODO: We should probably also manually abort the fetch task, to reclaim\n      // server bandwidth.\n      route.staleAt = now + 60 * 1000\n\n      // Upgrade to Pending so we know there's already a request in progress\n      route.status = EntryStatus.Pending\n\n      // Intentional fallthrough to the Pending branch\n    }\n    case EntryStatus.Pending: {\n      // Still pending. We can't start prefetching the segments until the route\n      // tree has loaded. Add the task to the set of blocked tasks so that it\n      // is notified when the route tree is ready.\n      const blockedTasks = route.blockedTasks\n      if (blockedTasks === null) {\n        route.blockedTasks = new Set([task])\n      } else {\n        blockedTasks.add(task)\n      }\n      return PrefetchTaskExitStatus.Blocked\n    }\n    case EntryStatus.Rejected: {\n      // Route tree failed to load. Treat as a 404.\n      return PrefetchTaskExitStatus.Done\n    }\n    case EntryStatus.Fulfilled: {\n      if (task.phase !== PrefetchPhase.Segments) {\n        // Do not prefetch segment data until we've entered the segment phase.\n        return PrefetchTaskExitStatus.Done\n      }\n      // Recursively fill in the segment tree.\n      if (!hasNetworkBandwidth()) {\n        // Stop prefetching segments until there's more bandwidth.\n        return PrefetchTaskExitStatus.InProgress\n      }\n      const tree = route.tree\n\n      // Determine which fetch strategy to use for this prefetch task.\n      const fetchStrategy = task.includeDynamicData\n        ? FetchStrategy.Full\n        : route.isPPREnabled\n          ? FetchStrategy.PPR\n          : FetchStrategy.LoadingBoundary\n\n      switch (fetchStrategy) {\n        case FetchStrategy.PPR:\n          // Individually prefetch the static shell for each segment. This is\n          // the default prefetching behavior for static routes, or when PPR is\n          // enabled. It will not include any dynamic data.\n          return pingPPRRouteTree(now, task, route, tree)\n        case FetchStrategy.Full:\n        case FetchStrategy.LoadingBoundary: {\n          // Prefetch multiple segments using a single dynamic request.\n          const spawnedEntries = new Map<string, PendingSegmentCacheEntry>()\n          const dynamicRequestTree = diffRouteTreeAgainstCurrent(\n            now,\n            task,\n            route,\n            task.treeAtTimeOfPrefetch,\n            tree,\n            spawnedEntries,\n            fetchStrategy\n          )\n          const needsDynamicRequest = spawnedEntries.size > 0\n          if (needsDynamicRequest) {\n            // Perform a dynamic prefetch request and populate the cache with\n            // the result\n            spawnPrefetchSubtask(\n              fetchSegmentPrefetchesUsingDynamicRequest(\n                task,\n                route,\n                fetchStrategy,\n                dynamicRequestTree,\n                spawnedEntries\n              )\n            )\n          }\n          return PrefetchTaskExitStatus.Done\n        }\n        default:\n          fetchStrategy satisfies never\n      }\n      break\n    }\n    default: {\n      route satisfies never\n    }\n  }\n  return PrefetchTaskExitStatus.Done\n}\n\nfunction pingPPRRouteTree(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  tree: RouteTree\n): PrefetchTaskExitStatus.InProgress | PrefetchTaskExitStatus.Done {\n  const segment = readOrCreateSegmentCacheEntry(now, task, route, tree.key)\n  pingPerSegment(now, task, route, segment, task.key, tree.key)\n  if (tree.slots !== null) {\n    if (!hasNetworkBandwidth()) {\n      // Stop prefetching segments until there's more bandwidth.\n      return PrefetchTaskExitStatus.InProgress\n    }\n    // Recursively ping the children.\n    for (const parallelRouteKey in tree.slots) {\n      const childTree = tree.slots[parallelRouteKey]\n      const childExitStatus = pingPPRRouteTree(now, task, route, childTree)\n      if (childExitStatus === PrefetchTaskExitStatus.InProgress) {\n        // Child yielded without finishing.\n        return PrefetchTaskExitStatus.InProgress\n      }\n    }\n  }\n  // This segment and all its children have finished prefetching.\n  return PrefetchTaskExitStatus.Done\n}\n\nfunction diffRouteTreeAgainstCurrent(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  oldTree: FlightRouterState,\n  newTree: RouteTree,\n  spawnedEntries: Map<string, PendingSegmentCacheEntry>,\n  fetchStrategy: FetchStrategy.Full | FetchStrategy.LoadingBoundary\n): FlightRouterState {\n  // This is a single recursive traversal that does multiple things:\n  // - Finds the parts of the target route (newTree) that are not part of\n  //   of the current page (oldTree) by diffing them, using the same algorithm\n  //   as a real navigation.\n  // - Constructs a request tree (FlightRouterState) that describes which\n  //   segments need to be prefetched and which ones are already cached.\n  // - Creates a set of pending cache entries for the segments that need to\n  //   be prefetched, so that a subsequent prefetch task does not request the\n  //   same segments again.\n  const oldTreeChildren = oldTree[1]\n  const newTreeChildren = newTree.slots\n  let requestTreeChildren: Record<string, FlightRouterState> = {}\n  if (newTreeChildren !== null) {\n    for (const parallelRouteKey in newTreeChildren) {\n      const newTreeChild = newTreeChildren[parallelRouteKey]\n      const newTreeChildSegment = newTreeChild.segment\n      const oldTreeChild: FlightRouterState | void =\n        oldTreeChildren[parallelRouteKey]\n      const oldTreeChildSegment: FlightRouterStateSegment | void =\n        oldTreeChild?.[0]\n      if (\n        oldTreeChildSegment !== undefined &&\n        matchSegment(newTreeChildSegment, oldTreeChildSegment)\n      ) {\n        // This segment is already part of the current route. Keep traversing.\n        const requestTreeChild = diffRouteTreeAgainstCurrent(\n          now,\n          task,\n          route,\n          oldTreeChild,\n          newTreeChild,\n          spawnedEntries,\n          fetchStrategy\n        )\n        requestTreeChildren[parallelRouteKey] = requestTreeChild\n      } else {\n        // This segment is not part of the current route. We're entering a\n        // part of the tree that we need to prefetch (unless everything is\n        // already cached).\n        switch (fetchStrategy) {\n          case FetchStrategy.LoadingBoundary: {\n            // When PPR is disabled, we can't prefetch per segment. We must\n            // fallback to the old prefetch behavior and send a dynamic request.\n            // Only routes that include a loading boundary can be prefetched in\n            // this way.\n            //\n            // This is simlar to a \"full\" prefetch, but we're much more\n            // conservative about which segments to include in the request.\n            //\n            // The server will only render up to the first loading boundary\n            // inside new part of the tree. If there's no loading boundary, the\n            // server will never return any data. TODO: When we prefetch the\n            // route tree, the server should indicate whether there's a loading\n            // boundary so the client doesn't send a second request for no\n            // reason.\n            const requestTreeChild =\n              pingPPRDisabledRouteTreeUpToLoadingBoundary(\n                now,\n                task,\n                route,\n                newTreeChild,\n                null,\n                spawnedEntries\n              )\n            requestTreeChildren[parallelRouteKey] = requestTreeChild\n            break\n          }\n          case FetchStrategy.Full: {\n            // This is a \"full\" prefetch. Fetch all the data in the tree, both\n            // static and dynamic. We issue roughly the same request that we\n            // would during a real navigation. The goal is that once the\n            // navigation occurs, the router should not have to fetch any\n            // additional data.\n            //\n            // Although the response will include dynamic data, opting into a\n            // Full prefetch — via <Link prefetch={true}> — implicitly\n            // instructs the cache to treat the response as \"static\", or non-\n            // dynamic, since the whole point is to cache it for\n            // future navigations.\n            //\n            // Construct a tree (currently a FlightRouterState) that represents\n            // which segments need to be prefetched and which ones are already\n            // cached. If the tree is empty, then we can exit. Otherwise, we'll\n            // send the request tree to the server and use the response to\n            // populate the segment cache.\n            const requestTreeChild = pingRouteTreeAndIncludeDynamicData(\n              now,\n              task,\n              route,\n              newTreeChild,\n              false,\n              spawnedEntries\n            )\n            requestTreeChildren[parallelRouteKey] = requestTreeChild\n            break\n          }\n          default:\n            fetchStrategy satisfies never\n        }\n      }\n    }\n  }\n  const requestTree: FlightRouterState = [\n    newTree.segment,\n    requestTreeChildren,\n    null,\n    null,\n    newTree.isRootLayout,\n  ]\n  return requestTree\n}\n\nfunction pingPPRDisabledRouteTreeUpToLoadingBoundary(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  tree: RouteTree,\n  refetchMarkerContext: 'refetch' | 'inside-shared-layout' | null,\n  spawnedEntries: Map<string, PendingSegmentCacheEntry>\n): FlightRouterState {\n  // This function is similar to pingRouteTreeAndIncludeDynamicData, except the\n  // server is only going to return a minimal loading state — it will stop\n  // rendering at the first loading boundary. Whereas a Full prefetch is\n  // intentionally aggressive and tries to pretfetch all the data that will be\n  // needed for a navigation, a LoadingBoundary prefetch is much more\n  // conservative. For example, it will omit from the request tree any segment\n  // that is already cached, regardles of whether it's partial or full. By\n  // contrast, a Full prefetch will refetch partial segments.\n\n  // \"inside-shared-layout\" tells the server where to start looking for a\n  // loading boundary.\n  let refetchMarker: 'refetch' | 'inside-shared-layout' | null =\n    refetchMarkerContext === null ? 'inside-shared-layout' : null\n\n  const segment = readOrCreateSegmentCacheEntry(now, task, route, tree.key)\n  switch (segment.status) {\n    case EntryStatus.Empty: {\n      // This segment is not cached. Add a refetch marker so the server knows\n      // to start rendering here.\n      // TODO: Instead of a \"refetch\" marker, we could just omit this subtree's\n      // FlightRouterState from the request tree. I think this would probably\n      // already work even without any updates to the server. For consistency,\n      // though, I'll send the full tree and we'll look into this later as part\n      // of a larger redesign of the request protocol.\n\n      // Add the pending cache entry to the result map.\n      spawnedEntries.set(\n        tree.key,\n        upgradeToPendingSegment(\n          segment,\n          // Set the fetch strategy to LoadingBoundary to indicate that the server\n          // might not include it in the pending response. If another route is able\n          // to issue a per-segment request, we'll do that in the background.\n          FetchStrategy.LoadingBoundary\n        )\n      )\n      if (refetchMarkerContext !== 'refetch') {\n        refetchMarker = refetchMarkerContext = 'refetch'\n      } else {\n        // There's already a parent with a refetch marker, so we don't need\n        // to add another one.\n      }\n      break\n    }\n    case EntryStatus.Fulfilled: {\n      // The segment is already cached.\n      // TODO: The server should include a `hasLoading` field as part of the\n      // route tree prefetch.\n      if (segment.loading !== null) {\n        // This segment has a loading boundary, which means the server won't\n        // render its children. So there's nothing left to prefetch along this\n        // path. We can bail out.\n        return convertRouteTreeToFlightRouterState(tree)\n      }\n      // NOTE: If the cached segment were fetched using PPR, then it might be\n      // partial. We could get a more complete version of the segment by\n      // including it in this non-PPR request.\n      //\n      // We're intentionally choosing not to, though, because it's generally\n      // better to avoid doing a dynamic prefetch whenever possible.\n      break\n    }\n    case EntryStatus.Pending: {\n      // There's another prefetch currently in progress. Don't add the refetch\n      // marker yet, so the server knows it can skip rendering this segment.\n      break\n    }\n    case EntryStatus.Rejected: {\n      // The segment failed to load. We shouldn't issue another request until\n      // the stale time has elapsed.\n      break\n    }\n    default:\n      segment satisfies never\n  }\n  const requestTreeChildren: Record<string, FlightRouterState> = {}\n  if (tree.slots !== null) {\n    for (const parallelRouteKey in tree.slots) {\n      const childTree = tree.slots[parallelRouteKey]\n      requestTreeChildren[parallelRouteKey] =\n        pingPPRDisabledRouteTreeUpToLoadingBoundary(\n          now,\n          task,\n          route,\n          childTree,\n          refetchMarkerContext,\n          spawnedEntries\n        )\n    }\n  }\n  const requestTree: FlightRouterState = [\n    tree.segment,\n    requestTreeChildren,\n    null,\n    refetchMarker,\n    tree.isRootLayout,\n  ]\n  return requestTree\n}\n\nfunction pingRouteTreeAndIncludeDynamicData(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  tree: RouteTree,\n  isInsideRefetchingParent: boolean,\n  spawnedEntries: Map<string, PendingSegmentCacheEntry>\n): FlightRouterState {\n  // The tree we're constructing is the same shape as the tree we're navigating\n  // to. But even though this is a \"new\" tree, some of the individual segments\n  // may be cached as a result of other route prefetches.\n  //\n  // So we need to find the first uncached segment along each path add an\n  // explicit \"refetch\" marker so the server knows where to start rendering.\n  // Once the server starts rendering along a path, it keeps rendering the\n  // entire subtree.\n  const segment = readOrCreateSegmentCacheEntry(now, task, route, tree.key)\n\n  let spawnedSegment: PendingSegmentCacheEntry | null = null\n\n  switch (segment.status) {\n    case EntryStatus.Empty: {\n      // This segment is not cached. Include it in the request.\n      spawnedSegment = upgradeToPendingSegment(segment, FetchStrategy.Full)\n      break\n    }\n    case EntryStatus.Fulfilled: {\n      // The segment is already cached.\n      if (segment.isPartial) {\n        // The cached segment contians dynamic holes. Since this is a Full\n        // prefetch, we need to include it in the request.\n        spawnedSegment = pingFullSegmentRevalidation(\n          now,\n          task,\n          route,\n          segment,\n          tree.key\n        )\n      }\n      break\n    }\n    case EntryStatus.Pending:\n    case EntryStatus.Rejected: {\n      // There's either another prefetch currently in progress, or the previous\n      // attempt failed. If it wasn't a Full prefetch, fetch it again.\n      if (segment.fetchStrategy !== FetchStrategy.Full) {\n        spawnedSegment = pingFullSegmentRevalidation(\n          now,\n          task,\n          route,\n          segment,\n          tree.key\n        )\n      }\n      break\n    }\n    default:\n      segment satisfies never\n  }\n  const requestTreeChildren: Record<string, FlightRouterState> = {}\n  if (tree.slots !== null) {\n    for (const parallelRouteKey in tree.slots) {\n      const childTree = tree.slots[parallelRouteKey]\n      requestTreeChildren[parallelRouteKey] =\n        pingRouteTreeAndIncludeDynamicData(\n          now,\n          task,\n          route,\n          childTree,\n          isInsideRefetchingParent || spawnedSegment !== null,\n          spawnedEntries\n        )\n    }\n  }\n\n  if (spawnedSegment !== null) {\n    // Add the pending entry to the result map.\n    spawnedEntries.set(tree.key, spawnedSegment)\n  }\n\n  // Don't bother to add a refetch marker if one is already present in a parent.\n  const refetchMarker =\n    !isInsideRefetchingParent && spawnedSegment !== null ? 'refetch' : null\n\n  const requestTree: FlightRouterState = [\n    tree.segment,\n    requestTreeChildren,\n    null,\n    refetchMarker,\n    tree.isRootLayout,\n  ]\n  return requestTree\n}\n\nfunction pingPerSegment(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  segment: SegmentCacheEntry,\n  routeKey: RouteCacheKey,\n  segmentKey: string\n): void {\n  switch (segment.status) {\n    case EntryStatus.Empty:\n      // Upgrade to Pending so we know there's already a request in progress\n      spawnPrefetchSubtask(\n        fetchSegmentOnCacheMiss(\n          route,\n          upgradeToPendingSegment(segment, FetchStrategy.PPR),\n          routeKey,\n          segmentKey\n        )\n      )\n      break\n    case EntryStatus.Pending: {\n      // There's already a request in progress. Depending on what kind of\n      // request it is, we may want to revalidate it.\n      switch (segment.fetchStrategy) {\n        case FetchStrategy.PPR:\n        case FetchStrategy.Full:\n          // There's already a request in progress. Don't do anything.\n          break\n        case FetchStrategy.LoadingBoundary:\n          // There's a pending request, but because it's using the old\n          // prefetching strategy, we can't be sure if it will be fulfilled by\n          // the response — it might be inside the loading boundary. Perform\n          // a revalidation, but because it's speculative, wait to do it at\n          // background priority.\n          if (background(task)) {\n            // TODO: Instead of speculatively revalidating, consider including\n            // `hasLoading` in the route tree prefetch response.\n            pingPPRSegmentRevalidation(\n              now,\n              task,\n              segment,\n              route,\n              routeKey,\n              segmentKey\n            )\n          }\n          break\n        default:\n          segment.fetchStrategy satisfies never\n      }\n      break\n    }\n    case EntryStatus.Rejected: {\n      // The existing entry in the cache was rejected. Depending on how it\n      // was originally fetched, we may or may not want to revalidate it.\n      switch (segment.fetchStrategy) {\n        case FetchStrategy.PPR:\n        case FetchStrategy.Full:\n          // The previous attempt to fetch this entry failed. Don't attempt to\n          // fetch it again until the entry expires.\n          break\n        case FetchStrategy.LoadingBoundary:\n          // There's a rejected entry, but it was fetched using the loading\n          // boundary strategy. So the reason it wasn't returned by the server\n          // might just be because it was inside a loading boundary. Or because\n          // there was a dynamic rewrite. Revalidate it using the per-\n          // segment strategy.\n          //\n          // Because a rejected segment will definitely prevent the segment (and\n          // all of its children) from rendering, we perform this revalidation\n          // immediately instead of deferring it to a background task.\n          pingPPRSegmentRevalidation(\n            now,\n            task,\n            segment,\n            route,\n            routeKey,\n            segmentKey\n          )\n          break\n        default:\n          segment.fetchStrategy satisfies never\n      }\n      break\n    }\n    case EntryStatus.Fulfilled:\n      // Segment is already cached. There's nothing left to prefetch.\n      break\n    default:\n      segment satisfies never\n  }\n\n  // Segments do not have dependent tasks, so once the prefetch is initiated,\n  // there's nothing else for us to do (except write the server data into the\n  // entry, which is handled by `fetchSegmentOnCacheMiss`).\n}\n\nfunction pingPPRSegmentRevalidation(\n  now: number,\n  task: PrefetchTask,\n  currentSegment: SegmentCacheEntry,\n  route: FulfilledRouteCacheEntry,\n  routeKey: RouteCacheKey,\n  segmentKey: string\n): void {\n  const revalidatingSegment = readOrCreateRevalidatingSegmentEntry(\n    now,\n    currentSegment\n  )\n  switch (revalidatingSegment.status) {\n    case EntryStatus.Empty:\n      // Spawn a prefetch request and upsert the segment into the cache\n      // upon completion.\n      upsertSegmentOnCompletion(\n        task,\n        route,\n        segmentKey,\n        spawnPrefetchSubtask(\n          fetchSegmentOnCacheMiss(\n            route,\n            upgradeToPendingSegment(revalidatingSegment, FetchStrategy.PPR),\n            routeKey,\n            segmentKey\n          )\n        )\n      )\n      break\n    case EntryStatus.Pending:\n      // There's already a revalidation in progress.\n      break\n    case EntryStatus.Fulfilled:\n    case EntryStatus.Rejected:\n      // A previous revalidation attempt finished, but we chose not to replace\n      // the existing entry in the cache. Don't try again until or unless the\n      // revalidation entry expires.\n      break\n    default:\n      revalidatingSegment satisfies never\n  }\n}\n\nfunction pingFullSegmentRevalidation(\n  now: number,\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  currentSegment: SegmentCacheEntry,\n  segmentKey: string\n): PendingSegmentCacheEntry | null {\n  const revalidatingSegment = readOrCreateRevalidatingSegmentEntry(\n    now,\n    currentSegment\n  )\n  if (revalidatingSegment.status === EntryStatus.Empty) {\n    // During a Full prefetch, a single dynamic request is made for all the\n    // segments that we need. So we don't initiate a request here directly. By\n    // returning a pending entry from this function, it signals to the caller\n    // that this segment should be included in the request that's sent to\n    // the server.\n    const pendingSegment = upgradeToPendingSegment(\n      revalidatingSegment,\n      FetchStrategy.Full\n    )\n    upsertSegmentOnCompletion(\n      task,\n      route,\n      segmentKey,\n      waitForSegmentCacheEntry(pendingSegment)\n    )\n    return pendingSegment\n  } else {\n    // There's already a revalidation in progress.\n    const nonEmptyRevalidatingSegment = revalidatingSegment\n    if (nonEmptyRevalidatingSegment.fetchStrategy !== FetchStrategy.Full) {\n      // The existing revalidation was not fetched using the Full strategy.\n      // Reset it and start a new revalidation.\n      const emptySegment = resetRevalidatingSegmentEntry(\n        nonEmptyRevalidatingSegment\n      )\n      const pendingSegment = upgradeToPendingSegment(\n        emptySegment,\n        FetchStrategy.Full\n      )\n      upsertSegmentOnCompletion(\n        task,\n        route,\n        segmentKey,\n        waitForSegmentCacheEntry(pendingSegment)\n      )\n      return pendingSegment\n    }\n    switch (nonEmptyRevalidatingSegment.status) {\n      case EntryStatus.Pending:\n        // There's already an in-progress prefetch that includes this segment.\n        return null\n      case EntryStatus.Fulfilled:\n      case EntryStatus.Rejected:\n        // A previous revalidation attempt finished, but we chose not to replace\n        // the existing entry in the cache. Don't try again until or unless the\n        // revalidation entry expires.\n        return null\n      default:\n        nonEmptyRevalidatingSegment satisfies never\n        return null\n    }\n  }\n}\n\nconst noop = () => {}\n\nfunction upsertSegmentOnCompletion(\n  task: PrefetchTask,\n  route: FulfilledRouteCacheEntry,\n  key: string,\n  promise: Promise<FulfilledSegmentCacheEntry | null>\n) {\n  // Wait for a segment to finish loading, then upsert it into the cache\n  promise.then((fulfilled) => {\n    if (fulfilled !== null) {\n      // Received new data. Attempt to replace the existing entry in the cache.\n      const keypath = getSegmentKeypathForTask(task, route, key)\n      upsertSegmentEntry(Date.now(), keypath, fulfilled)\n    }\n  }, noop)\n}\n\n// -----------------------------------------------------------------------------\n// The remainder of the module is a MinHeap implementation. Try not to put any\n// logic below here unless it's related to the heap algorithm. We can extract\n// this to a separate module if/when we need multiple kinds of heaps.\n// -----------------------------------------------------------------------------\n\nfunction compareQueuePriority(a: PrefetchTask, b: PrefetchTask) {\n  // Since the queue is a MinHeap, this should return a positive number if b is\n  // higher priority than a, and a negative number if a is higher priority\n  // than b.\n\n  // `priority` is an integer, where higher numbers are higher priority.\n  const priorityDiff = b.priority - a.priority\n  if (priorityDiff !== 0) {\n    return priorityDiff\n  }\n\n  // If the priority is the same, check which phase the prefetch is in — is it\n  // prefetching the route tree, or the segments? Route trees are prioritized.\n  const phaseDiff = b.phase - a.phase\n  if (phaseDiff !== 0) {\n    return phaseDiff\n  }\n\n  // Finally, check the insertion order. `sortId` is an incrementing counter\n  // assigned to prefetches. We want to process the newest prefetches first.\n  return b.sortId - a.sortId\n}\n\nfunction heapPush(heap: Array<PrefetchTask>, node: PrefetchTask): void {\n  const index = heap.length\n  heap.push(node)\n  node._heapIndex = index\n  heapSiftUp(heap, node, index)\n}\n\nfunction heapPeek(heap: Array<PrefetchTask>): PrefetchTask | null {\n  return heap.length === 0 ? null : heap[0]\n}\n\nfunction heapPop(heap: Array<PrefetchTask>): PrefetchTask | null {\n  if (heap.length === 0) {\n    return null\n  }\n  const first = heap[0]\n  first._heapIndex = -1\n  const last = heap.pop() as PrefetchTask\n  if (last !== first) {\n    heap[0] = last\n    last._heapIndex = 0\n    heapSiftDown(heap, last, 0)\n  }\n  return first\n}\n\nfunction heapDelete(heap: Array<PrefetchTask>, node: PrefetchTask): void {\n  const index = node._heapIndex\n  if (index !== -1) {\n    node._heapIndex = -1\n    if (heap.length !== 0) {\n      const last = heap.pop() as PrefetchTask\n      if (last !== node) {\n        heap[index] = last\n        last._heapIndex = index\n        heapSiftDown(heap, last, index)\n      }\n    }\n  }\n}\n\nfunction heapResift(heap: Array<PrefetchTask>, node: PrefetchTask): void {\n  const index = node._heapIndex\n  if (index !== -1) {\n    if (index === 0) {\n      heapSiftDown(heap, node, 0)\n    } else {\n      const parentIndex = (index - 1) >>> 1\n      const parent = heap[parentIndex]\n      if (compareQueuePriority(parent, node) > 0) {\n        // The parent is larger. Sift up.\n        heapSiftUp(heap, node, index)\n      } else {\n        // The parent is smaller (or equal). Sift down.\n        heapSiftDown(heap, node, index)\n      }\n    }\n  }\n}\n\nfunction heapSiftUp(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  while (index > 0) {\n    const parentIndex = (index - 1) >>> 1\n    const parent = heap[parentIndex]\n    if (compareQueuePriority(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node\n      node._heapIndex = parentIndex\n      heap[index] = parent\n      parent._heapIndex = index\n\n      index = parentIndex\n    } else {\n      // The parent is smaller. Exit.\n      return\n    }\n  }\n}\n\nfunction heapSiftDown(\n  heap: Array<PrefetchTask>,\n  node: PrefetchTask,\n  i: number\n): void {\n  let index = i\n  const length = heap.length\n  const halfLength = length >>> 1\n  while (index < halfLength) {\n    const leftIndex = (index + 1) * 2 - 1\n    const left = heap[leftIndex]\n    const rightIndex = leftIndex + 1\n    const right = heap[rightIndex]\n\n    // If the left or right node is smaller, swap with the smaller of those.\n    if (compareQueuePriority(left, node) < 0) {\n      if (rightIndex < length && compareQueuePriority(right, left) < 0) {\n        heap[index] = right\n        right._heapIndex = index\n        heap[rightIndex] = node\n        node._heapIndex = rightIndex\n\n        index = rightIndex\n      } else {\n        heap[index] = left\n        left._heapIndex = index\n        heap[leftIndex] = node\n        node._heapIndex = leftIndex\n\n        index = leftIndex\n      }\n    } else if (rightIndex < length && compareQueuePriority(right, node) < 0) {\n      heap[index] = right\n      right._heapIndex = index\n      heap[rightIndex] = node\n      node._heapIndex = rightIndex\n\n      index = rightIndex\n    } else {\n      // Neither child is smaller. Exit.\n      return\n    }\n  }\n}\n"], "names": ["matchSegment", "readOrCreateRouteCacheEntry", "readOrCreateSegmentCacheEntry", "fetchRouteOnCacheMiss", "fetchSegmentOnCacheMiss", "EntryStatus", "fetchSegmentPrefetchesUsingDynamicRequest", "convertRouteTreeToFlightRouterState", "FetchStrategy", "readOrCreateRevalidatingSegmentEntry", "upsertSegmentEntry", "upgradeToPendingSegment", "waitForSegmentCacheEntry", "resetRevalidatingSegmentEntry", "getSegmentKeypathForTask", "PrefetchPriority", "scheduleMicrotask", "queueMicrotask", "fn", "Promise", "resolve", "then", "catch", "error", "setTimeout", "taskHeap", "MAX_CONCURRENT_PREFETCH_REQUESTS", "inProgressRequests", "sortIdCounter", "didScheduleMicrotask", "schedulePrefetchTask", "key", "treeAtTimeOfPrefetch", "includeDynamicData", "priority", "task", "phase", "hasBackgroundWork", "sortId", "isCanceled", "_heapIndex", "heapPush", "ensureWorkIsScheduled", "cancelPrefetchTask", "heapDelete", "reschedulePrefetchTask", "heapResift", "hasNetworkBandwidth", "processQueueInMicrotask", "spawnPrefetchSubtask", "prefetchSubtask", "result", "onPrefetchConnectionClosed", "closed", "value", "pingPrefetchTask", "now", "Date", "heapPeek", "route", "exitStatus", "pingRootRouteTree", "heapPop", "Background", "background", "status", "Empty", "staleAt", "Pending", "blockedTasks", "Set", "add", "Rejected", "Fulfilled", "tree", "fetchStrategy", "Full", "isPPREnabled", "PPR", "LoadingBoundary", "pingPPRRouteTree", "spawnedEntries", "Map", "dynamicRequestTree", "diffRouteTreeAgainstCurrent", "needsDynamicRequest", "size", "segment", "pingPerSegment", "slots", "parallelRouteKey", "childTree", "childExitStatus", "oldTree", "newTree", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newTreeC<PERSON><PERSON>n", "requestTreeChildren", "new<PERSON>ree<PERSON><PERSON>d", "newTreeChildSegment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldTreeChildSegment", "undefined", "requestTreeChild", "pingPPRDisabledRouteTreeUpToLoadingBoundary", "pingRouteTreeAndIncludeDynamicData", "requestTree", "isRootLayout", "refetchMarkerContext", "refetch<PERSON><PERSON><PERSON>", "set", "loading", "isInsideRefetchingParent", "spawnedSegment", "isPartial", "pingFullSegmentRevalidation", "routeKey", "segmentKey", "pingPPRSegmentRevalidation", "currentSegment", "revalidatingSegment", "upsertSegmentOnCompletion", "pendingSegment", "nonEmptyRevalidatingSegment", "emptySegment", "noop", "promise", "fulfilled", "keypath", "compareQueuePriority", "a", "b", "priorityDiff", "phaseDiff", "heap", "node", "index", "length", "push", "heapSiftUp", "first", "last", "pop", "heapSiftDown", "parentIndex", "parent", "i", "<PERSON><PERSON><PERSON><PERSON>", "leftIndex", "left", "rightIndex", "right"], "mappings": "AAIA,SAASA,YAAY,QAAQ,oBAAmB;AAChD,SACEC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,qBAAqB,EACrBC,uBAAuB,EACvBC,WAAW,EAKXC,yCAAyC,EAEzCC,mCAAmC,EACnCC,aAAa,EACbC,oCAAoC,EACpCC,kBAAkB,EAElBC,uBAAuB,EACvBC,wBAAwB,EACxBC,6BAA6B,EAC7BC,wBAAwB,QACnB,UAAS;AAEhB,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,MAAMC,oBACJ,OAAOC,mBAAmB,aACtBA,iBACA,CAACC,KACCC,QAAQC,OAAO,GACZC,IAAI,CAACH,IACLI,KAAK,CAAC,CAACC,QACNC,WAAW;YACT,MAAMD;QACR;;;AAuHZ,MAAME,WAAgC,EAAE;AAExC,6EAA6E;AAC7E,0EAA0E;AAC1E,MAAMC,mCAAmC;AACzC,IAAIC,qBAAqB;AAEzB,IAAIC,gBAAgB;AACpB,IAAIC,uBAAuB;AAE3B;;;;;;;;;;;CAWC,GACD,OAAO,SAASC,qBACdC,GAAkB,EAClBC,oBAAuC,EACvCC,kBAA2B,EAC3BC,QAA0B;IAE1B,4BAA4B;IAC5B,MAAMC,OAAqB;QACzBJ;QACAC;QACAE;QACAE,KAAK;QACLC,mBAAmB;QACnBJ;QACAK,QAAQV;QACRW,YAAY;QACZC,YAAY,CAAC;IACf;IACAC,SAAShB,UAAUU;IAEnB,+CAA+C;IAC/C,EAAE;IACF,yEAAyE;IACzE,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,qBAAqB;IACrBO;IAEA,OAAOP;AACT;AAEA,OAAO,SAASQ,mBAAmBR,IAAkB;IACnD,0EAA0E;IAC1E,wBAAwB;IACxB,EAAE;IACF,2EAA2E;IAC3E,wEAAwE;IACxEA,KAAKI,UAAU,GAAG;IAClBK,WAAWnB,UAAUU;AACvB;AAEA,OAAO,SAASU,uBACdV,IAAkB,EAClBH,oBAAuC,EACvCC,kBAA2B,EAC3BC,QAA0B;IAE1B,wEAAwE;IACxE,0EAA0E;IAC1E,mDAAmD;IACnD,EAAE;IACF,sEAAsE;IACtE,qBAAqB;IAErB,0DAA0D;IAC1DC,KAAKI,UAAU,GAAG;IAClBJ,KAAKC,KAAK;IAEV,uEAAuE;IACvE,yDAAyD;IACzDD,KAAKG,MAAM,GAAGV;IACdO,KAAKD,QAAQ,GAAGA;IAEhBC,KAAKH,oBAAoB,GAAGA;IAC5BG,KAAKF,kBAAkB,GAAGA;IAE1B,IAAIE,KAAKK,UAAU,KAAK,CAAC,GAAG;QAC1B,oCAAoC;QACpCM,WAAWrB,UAAUU;IACvB,OAAO;QACLM,SAAShB,UAAUU;IACrB;IACAO;AACF;AAEA,SAASA;IACP,IAAIb,wBAAwB,CAACkB,uBAAuB;QAClD,wEAAwE;QACxE,oEAAoE;QACpE,iEAAiE;QACjE;IACF;IACAlB,uBAAuB;IACvBb,kBAAkBgC;AACpB;AAEA;;;;;CAKC,GACD,SAASD;IACP,yEAAyE;IACzE,wEAAwE;IACxE,2EAA2E;IAC3E,sBAAsB;IACtB,OAAOpB,qBAAqBD;AAC9B;AAEA,SAASuB,qBACPC,eAAyD;IAEzD,sEAAsE;IACtE,0EAA0E;IAC1E,mCAAmC;IACnC,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,yEAAyE;IACzE,2EAA2E;IAC3E,kDAAkD;IAClDvB;IACA,OAAOuB,gBAAgB7B,IAAI,CAAC,CAAC8B;QAC3B,IAAIA,WAAW,MAAM;YACnB,iEAAiE;YACjE,mDAAmD;YACnDC;YACA,OAAO;QACT;QACA,qEAAqE;QACrED,OAAOE,MAAM,CAAChC,IAAI,CAAC+B;QACnB,OAAOD,OAAOG,KAAK;IACrB;AACF;AAEA,SAASF;IACPzB;IAEA,qEAAqE;IACrE,oBAAoB;IACpBe;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASa,iBAAiBpB,IAAkB;IACjD,yEAAyE;IACzE,IACE,kCAAkC;IAClCA,KAAKI,UAAU,IACf,uCAAuC;IACvCJ,KAAKK,UAAU,KAAK,CAAC,GACrB;QACA;IACF;IACA,kCAAkC;IAClCC,SAAShB,UAAUU;IACnBO;AACF;AAEA,SAASM;IACPnB,uBAAuB;IAEvB,0EAA0E;IAC1E,4EAA4E;IAC5E,wDAAwD;IACxD,MAAM2B,MAAMC,KAAKD,GAAG;IAEpB,gEAAgE;IAChE,IAAIrB,OAAOuB,SAASjC;IACpB,MAAOU,SAAS,QAAQY,sBAAuB;QAC7C,MAAMY,QAAQ1D,4BAA4BuD,KAAKrB;QAC/C,MAAMyB,aAAaC,kBAAkBL,KAAKrB,MAAMwB;QAEhD,0EAA0E;QAC1E,4BAA4B;QAC5B,MAAMtB,oBAAoBF,KAAKE,iBAAiB;QAChDF,KAAKE,iBAAiB,GAAG;QAEzB,OAAQuB;YACN;gBACE,oEAAoE;gBACpE,sDAAsD;gBACtD;YACF;gBACE,iEAAiE;gBACjE,4DAA4D;gBAC5DE,QAAQrC;gBACR,4BAA4B;gBAC5BU,OAAOuB,SAASjC;gBAChB;YACF;gBACE,IAAIU,KAAKC,KAAK,QAA8B;oBAC1C,8DAA8D;oBAC9D,gBAAgB;oBAChBD,KAAKC,KAAK;oBACVU,WAAWrB,UAAUU;gBACvB,OAAO,IAAIE,mBAAmB;oBAC5B,mEAAmE;oBACnE,0BAA0B;oBAC1BF,KAAKD,QAAQ,GAAGnB,iBAAiBgD,UAAU;oBAC3CjB,WAAWrB,UAAUU;gBACvB,OAAO;oBACL,uDAAuD;oBACvD2B,QAAQrC;gBACV;gBACAU,OAAOuB,SAASjC;gBAChB;YACF;gBACEmC;QACJ;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,SAASI,WAAW7B,IAAkB;IACpC,IAAIA,KAAKD,QAAQ,KAAKnB,iBAAiBgD,UAAU,EAAE;QACjD,OAAO;IACT;IACA5B,KAAKE,iBAAiB,GAAG;IACzB,OAAO;AACT;AAEA,SAASwB,kBACPL,GAAW,EACXrB,IAAkB,EAClBwB,KAAsB;IAEtB,OAAQA,MAAMM,MAAM;QAClB,KAAK5D,YAAY6D,KAAK;YAAE;gBACtB,uEAAuE;gBACvE,sEAAsE;gBACtE,wBAAwB;gBAExB,wEAAwE;gBACxE,uEAAuE;gBACvE,oBAAoB;gBACpB,EAAE;gBACF,yEAAyE;gBACzE,oEAAoE;gBACpE,wEAAwE;gBACxE,EAAE;gBACF,oCAAoC;gBACpCjB,qBAAqB9C,sBAAsBwD,OAAOxB;gBAElD,yEAAyE;gBACzE,wEAAwE;gBACxE,0EAA0E;gBAC1E,mBAAmB;gBACnB,0EAA0E;gBAC1E,oBAAoB;gBACpBwB,MAAMQ,OAAO,GAAGX,MAAM,KAAK;gBAE3B,sEAAsE;gBACtEG,MAAMM,MAAM,GAAG5D,YAAY+D,OAAO;YAElC,gDAAgD;YAClD;QACA,KAAK/D,YAAY+D,OAAO;YAAE;gBACxB,yEAAyE;gBACzE,uEAAuE;gBACvE,4CAA4C;gBAC5C,MAAMC,eAAeV,MAAMU,YAAY;gBACvC,IAAIA,iBAAiB,MAAM;oBACzBV,MAAMU,YAAY,GAAG,IAAIC,IAAI;wBAACnC;qBAAK;gBACrC,OAAO;oBACLkC,aAAaE,GAAG,CAACpC;gBACnB;gBACA;YACF;QACA,KAAK9B,YAAYmE,QAAQ;YAAE;gBACzB,6CAA6C;gBAC7C;YACF;QACA,KAAKnE,YAAYoE,SAAS;YAAE;gBAC1B,IAAItC,KAAKC,KAAK,QAA6B;oBACzC,sEAAsE;oBACtE;gBACF;gBACA,wCAAwC;gBACxC,IAAI,CAACW,uBAAuB;oBAC1B,0DAA0D;oBAC1D;gBACF;gBACA,MAAM2B,OAAOf,MAAMe,IAAI;gBAEvB,gEAAgE;gBAChE,MAAMC,gBAAgBxC,KAAKF,kBAAkB,GACzCzB,cAAcoE,IAAI,GAClBjB,MAAMkB,YAAY,GAChBrE,cAAcsE,GAAG,GACjBtE,cAAcuE,eAAe;gBAEnC,OAAQJ;oBACN,KAAKnE,cAAcsE,GAAG;wBACpB,mEAAmE;wBACnE,qEAAqE;wBACrE,iDAAiD;wBACjD,OAAOE,iBAAiBxB,KAAKrB,MAAMwB,OAAOe;oBAC5C,KAAKlE,cAAcoE,IAAI;oBACvB,KAAKpE,cAAcuE,eAAe;wBAAE;4BAClC,6DAA6D;4BAC7D,MAAME,iBAAiB,IAAIC;4BAC3B,MAAMC,qBAAqBC,4BACzB5B,KACArB,MACAwB,OACAxB,KAAKH,oBAAoB,EACzB0C,MACAO,gBACAN;4BAEF,MAAMU,sBAAsBJ,eAAeK,IAAI,GAAG;4BAClD,IAAID,qBAAqB;gCACvB,iEAAiE;gCACjE,aAAa;gCACbpC,qBACE3C,0CACE6B,MACAwB,OACAgB,eACAQ,oBACAF;4BAGN;4BACA;wBACF;oBACA;wBACEN;gBACJ;gBACA;YACF;QACA;YAAS;gBACPhB;YACF;IACF;IACA;AACF;AAEA,SAASqB,iBACPxB,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/Be,IAAe;IAEf,MAAMa,UAAUrF,8BAA8BsD,KAAKrB,MAAMwB,OAAOe,KAAK3C,GAAG;IACxEyD,eAAehC,KAAKrB,MAAMwB,OAAO4B,SAASpD,KAAKJ,GAAG,EAAE2C,KAAK3C,GAAG;IAC5D,IAAI2C,KAAKe,KAAK,KAAK,MAAM;QACvB,IAAI,CAAC1C,uBAAuB;YAC1B,0DAA0D;YAC1D;QACF;QACA,iCAAiC;QACjC,IAAK,MAAM2C,oBAAoBhB,KAAKe,KAAK,CAAE;YACzC,MAAME,YAAYjB,KAAKe,KAAK,CAACC,iBAAiB;YAC9C,MAAME,kBAAkBZ,iBAAiBxB,KAAKrB,MAAMwB,OAAOgC;YAC3D,IAAIC,uBAAuD;gBACzD,mCAAmC;gBACnC;YACF;QACF;IACF;IACA,+DAA+D;IAC/D;AACF;AAEA,SAASR,4BACP5B,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/BkC,OAA0B,EAC1BC,OAAkB,EAClBb,cAAqD,EACrDN,aAAiE;IAEjE,kEAAkE;IAClE,uEAAuE;IACvE,4EAA4E;IAC5E,0BAA0B;IAC1B,uEAAuE;IACvE,sEAAsE;IACtE,yEAAyE;IACzE,2EAA2E;IAC3E,yBAAyB;IACzB,MAAMoB,kBAAkBF,OAAO,CAAC,EAAE;IAClC,MAAMG,kBAAkBF,QAAQL,KAAK;IACrC,IAAIQ,sBAAyD,CAAC;IAC9D,IAAID,oBAAoB,MAAM;QAC5B,IAAK,MAAMN,oBAAoBM,gBAAiB;YAC9C,MAAME,eAAeF,eAAe,CAACN,iBAAiB;YACtD,MAAMS,sBAAsBD,aAAaX,OAAO;YAChD,MAAMa,eACJL,eAAe,CAACL,iBAAiB;YACnC,MAAMW,sBACJD,gCAAAA,YAAc,CAAC,EAAE;YACnB,IACEC,wBAAwBC,aACxBtG,aAAamG,qBAAqBE,sBAClC;gBACA,sEAAsE;gBACtE,MAAME,mBAAmBnB,4BACvB5B,KACArB,MACAwB,OACAyC,cACAF,cACAjB,gBACAN;gBAEFsB,mBAAmB,CAACP,iBAAiB,GAAGa;YAC1C,OAAO;gBACL,kEAAkE;gBAClE,kEAAkE;gBAClE,mBAAmB;gBACnB,OAAQ5B;oBACN,KAAKnE,cAAcuE,eAAe;wBAAE;4BAClC,+DAA+D;4BAC/D,oEAAoE;4BACpE,mEAAmE;4BACnE,YAAY;4BACZ,EAAE;4BACF,2DAA2D;4BAC3D,+DAA+D;4BAC/D,EAAE;4BACF,+DAA+D;4BAC/D,mEAAmE;4BACnE,gEAAgE;4BAChE,mEAAmE;4BACnE,8DAA8D;4BAC9D,UAAU;4BACV,MAAMwB,mBACJC,4CACEhD,KACArB,MACAwB,OACAuC,cACA,MACAjB;4BAEJgB,mBAAmB,CAACP,iBAAiB,GAAGa;4BACxC;wBACF;oBACA,KAAK/F,cAAcoE,IAAI;wBAAE;4BACvB,kEAAkE;4BAClE,gEAAgE;4BAChE,4DAA4D;4BAC5D,6DAA6D;4BAC7D,mBAAmB;4BACnB,EAAE;4BACF,iEAAiE;4BACjE,0DAA0D;4BAC1D,iEAAiE;4BACjE,oDAAoD;4BACpD,sBAAsB;4BACtB,EAAE;4BACF,mEAAmE;4BACnE,kEAAkE;4BAClE,mEAAmE;4BACnE,8DAA8D;4BAC9D,8BAA8B;4BAC9B,MAAM2B,mBAAmBE,mCACvBjD,KACArB,MACAwB,OACAuC,cACA,OACAjB;4BAEFgB,mBAAmB,CAACP,iBAAiB,GAAGa;4BACxC;wBACF;oBACA;wBACE5B;gBACJ;YACF;QACF;IACF;IACA,MAAM+B,cAAiC;QACrCZ,QAAQP,OAAO;QACfU;QACA;QACA;QACAH,QAAQa,YAAY;KACrB;IACD,OAAOD;AACT;AAEA,SAASF,4CACPhD,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/Be,IAAe,EACfkC,oBAA+D,EAC/D3B,cAAqD;IAErD,6EAA6E;IAC7E,wEAAwE;IACxE,sEAAsE;IACtE,4EAA4E;IAC5E,mEAAmE;IACnE,4EAA4E;IAC5E,wEAAwE;IACxE,2DAA2D;IAE3D,uEAAuE;IACvE,oBAAoB;IACpB,IAAI4B,gBACFD,yBAAyB,OAAO,yBAAyB;IAE3D,MAAMrB,UAAUrF,8BAA8BsD,KAAKrB,MAAMwB,OAAOe,KAAK3C,GAAG;IACxE,OAAQwD,QAAQtB,MAAM;QACpB,KAAK5D,YAAY6D,KAAK;YAAE;gBACtB,uEAAuE;gBACvE,2BAA2B;gBAC3B,yEAAyE;gBACzE,uEAAuE;gBACvE,wEAAwE;gBACxE,yEAAyE;gBACzE,gDAAgD;gBAEhD,iDAAiD;gBACjDe,eAAe6B,GAAG,CAChBpC,KAAK3C,GAAG,EACRpB,wBACE4E,SACA,wEAAwE;gBACxE,yEAAyE;gBACzE,mEAAmE;gBACnE/E,cAAcuE,eAAe;gBAGjC,IAAI6B,yBAAyB,WAAW;oBACtCC,gBAAgBD,uBAAuB;gBACzC,OAAO;gBACL,mEAAmE;gBACnE,sBAAsB;gBACxB;gBACA;YACF;QACA,KAAKvG,YAAYoE,SAAS;YAAE;gBAC1B,iCAAiC;gBACjC,sEAAsE;gBACtE,uBAAuB;gBACvB,IAAIc,QAAQwB,OAAO,KAAK,MAAM;oBAC5B,oEAAoE;oBACpE,sEAAsE;oBACtE,yBAAyB;oBACzB,OAAOxG,oCAAoCmE;gBAC7C;gBAOA;YACF;QACA,KAAKrE,YAAY+D,OAAO;YAAE;gBAGxB;YACF;QACA,KAAK/D,YAAYmE,QAAQ;YAAE;gBAGzB;YACF;QACA;YACEe;IACJ;IACA,MAAMU,sBAAyD,CAAC;IAChE,IAAIvB,KAAKe,KAAK,KAAK,MAAM;QACvB,IAAK,MAAMC,oBAAoBhB,KAAKe,KAAK,CAAE;YACzC,MAAME,YAAYjB,KAAKe,KAAK,CAACC,iBAAiB;YAC9CO,mBAAmB,CAACP,iBAAiB,GACnCc,4CACEhD,KACArB,MACAwB,OACAgC,WACAiB,sBACA3B;QAEN;IACF;IACA,MAAMyB,cAAiC;QACrChC,KAAKa,OAAO;QACZU;QACA;QACAY;QACAnC,KAAKiC,YAAY;KAClB;IACD,OAAOD;AACT;AAEA,SAASD,mCACPjD,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/Be,IAAe,EACfsC,wBAAiC,EACjC/B,cAAqD;IAErD,6EAA6E;IAC7E,4EAA4E;IAC5E,uDAAuD;IACvD,EAAE;IACF,uEAAuE;IACvE,0EAA0E;IAC1E,wEAAwE;IACxE,kBAAkB;IAClB,MAAMM,UAAUrF,8BAA8BsD,KAAKrB,MAAMwB,OAAOe,KAAK3C,GAAG;IAExE,IAAIkF,iBAAkD;IAEtD,OAAQ1B,QAAQtB,MAAM;QACpB,KAAK5D,YAAY6D,KAAK;YAAE;gBACtB,yDAAyD;gBACzD+C,iBAAiBtG,wBAAwB4E,SAAS/E,cAAcoE,IAAI;gBACpE;YACF;QACA,KAAKvE,YAAYoE,SAAS;YAAE;gBAC1B,iCAAiC;gBACjC,IAAIc,QAAQ2B,SAAS,EAAE;oBACrB,kEAAkE;oBAClE,kDAAkD;oBAClDD,iBAAiBE,4BACf3D,KACArB,MACAwB,OACA4B,SACAb,KAAK3C,GAAG;gBAEZ;gBACA;YACF;QACA,KAAK1B,YAAY+D,OAAO;QACxB,KAAK/D,YAAYmE,QAAQ;YAAE;gBACzB,yEAAyE;gBACzE,gEAAgE;gBAChE,IAAIe,QAAQZ,aAAa,KAAKnE,cAAcoE,IAAI,EAAE;oBAChDqC,iBAAiBE,4BACf3D,KACArB,MACAwB,OACA4B,SACAb,KAAK3C,GAAG;gBAEZ;gBACA;YACF;QACA;YACEwD;IACJ;IACA,MAAMU,sBAAyD,CAAC;IAChE,IAAIvB,KAAKe,KAAK,KAAK,MAAM;QACvB,IAAK,MAAMC,oBAAoBhB,KAAKe,KAAK,CAAE;YACzC,MAAME,YAAYjB,KAAKe,KAAK,CAACC,iBAAiB;YAC9CO,mBAAmB,CAACP,iBAAiB,GACnCe,mCACEjD,KACArB,MACAwB,OACAgC,WACAqB,4BAA4BC,mBAAmB,MAC/ChC;QAEN;IACF;IAEA,IAAIgC,mBAAmB,MAAM;QAC3B,2CAA2C;QAC3ChC,eAAe6B,GAAG,CAACpC,KAAK3C,GAAG,EAAEkF;IAC/B;IAEA,8EAA8E;IAC9E,MAAMJ,gBACJ,CAACG,4BAA4BC,mBAAmB,OAAO,YAAY;IAErE,MAAMP,cAAiC;QACrChC,KAAKa,OAAO;QACZU;QACA;QACAY;QACAnC,KAAKiC,YAAY;KAClB;IACD,OAAOD;AACT;AAEA,SAASlB,eACPhC,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/B4B,OAA0B,EAC1B6B,QAAuB,EACvBC,UAAkB;IAElB,OAAQ9B,QAAQtB,MAAM;QACpB,KAAK5D,YAAY6D,KAAK;YACpB,sEAAsE;YACtEjB,qBACE7C,wBACEuD,OACAhD,wBAAwB4E,SAAS/E,cAAcsE,GAAG,GAClDsC,UACAC;YAGJ;QACF,KAAKhH,YAAY+D,OAAO;YAAE;gBACxB,mEAAmE;gBACnE,+CAA+C;gBAC/C,OAAQmB,QAAQZ,aAAa;oBAC3B,KAAKnE,cAAcsE,GAAG;oBACtB,KAAKtE,cAAcoE,IAAI;wBAErB;oBACF,KAAKpE,cAAcuE,eAAe;wBAChC,4DAA4D;wBAC5D,oEAAoE;wBACpE,kEAAkE;wBAClE,iEAAiE;wBACjE,uBAAuB;wBACvB,IAAIf,WAAW7B,OAAO;4BACpB,kEAAkE;4BAClE,oDAAoD;4BACpDmF,2BACE9D,KACArB,MACAoD,SACA5B,OACAyD,UACAC;wBAEJ;wBACA;oBACF;wBACE9B,QAAQZ,aAAa;gBACzB;gBACA;YACF;QACA,KAAKtE,YAAYmE,QAAQ;YAAE;gBACzB,oEAAoE;gBACpE,mEAAmE;gBACnE,OAAQe,QAAQZ,aAAa;oBAC3B,KAAKnE,cAAcsE,GAAG;oBACtB,KAAKtE,cAAcoE,IAAI;wBAGrB;oBACF,KAAKpE,cAAcuE,eAAe;wBAChC,iEAAiE;wBACjE,oEAAoE;wBACpE,qEAAqE;wBACrE,4DAA4D;wBAC5D,oBAAoB;wBACpB,EAAE;wBACF,sEAAsE;wBACtE,oEAAoE;wBACpE,4DAA4D;wBAC5DuC,2BACE9D,KACArB,MACAoD,SACA5B,OACAyD,UACAC;wBAEF;oBACF;wBACE9B,QAAQZ,aAAa;gBACzB;gBACA;YACF;QACA,KAAKtE,YAAYoE,SAAS;YAExB;QACF;YACEc;IACJ;AAEA,2EAA2E;AAC3E,2EAA2E;AAC3E,yDAAyD;AAC3D;AAEA,SAAS+B,2BACP9D,GAAW,EACXrB,IAAkB,EAClBoF,cAAiC,EACjC5D,KAA+B,EAC/ByD,QAAuB,EACvBC,UAAkB;IAElB,MAAMG,sBAAsB/G,qCAC1B+C,KACA+D;IAEF,OAAQC,oBAAoBvD,MAAM;QAChC,KAAK5D,YAAY6D,KAAK;YACpB,iEAAiE;YACjE,mBAAmB;YACnBuD,0BACEtF,MACAwB,OACA0D,YACApE,qBACE7C,wBACEuD,OACAhD,wBAAwB6G,qBAAqBhH,cAAcsE,GAAG,GAC9DsC,UACAC;YAIN;QACF,KAAKhH,YAAY+D,OAAO;YAEtB;QACF,KAAK/D,YAAYoE,SAAS;QAC1B,KAAKpE,YAAYmE,QAAQ;YAIvB;QACF;YACEgD;IACJ;AACF;AAEA,SAASL,4BACP3D,GAAW,EACXrB,IAAkB,EAClBwB,KAA+B,EAC/B4D,cAAiC,EACjCF,UAAkB;IAElB,MAAMG,sBAAsB/G,qCAC1B+C,KACA+D;IAEF,IAAIC,oBAAoBvD,MAAM,KAAK5D,YAAY6D,KAAK,EAAE;QACpD,uEAAuE;QACvE,0EAA0E;QAC1E,yEAAyE;QACzE,qEAAqE;QACrE,cAAc;QACd,MAAMwD,iBAAiB/G,wBACrB6G,qBACAhH,cAAcoE,IAAI;QAEpB6C,0BACEtF,MACAwB,OACA0D,YACAzG,yBAAyB8G;QAE3B,OAAOA;IACT,OAAO;QACL,8CAA8C;QAC9C,MAAMC,8BAA8BH;QACpC,IAAIG,4BAA4BhD,aAAa,KAAKnE,cAAcoE,IAAI,EAAE;YACpE,qEAAqE;YACrE,yCAAyC;YACzC,MAAMgD,eAAe/G,8BACnB8G;YAEF,MAAMD,iBAAiB/G,wBACrBiH,cACApH,cAAcoE,IAAI;YAEpB6C,0BACEtF,MACAwB,OACA0D,YACAzG,yBAAyB8G;YAE3B,OAAOA;QACT;QACA,OAAQC,4BAA4B1D,MAAM;YACxC,KAAK5D,YAAY+D,OAAO;gBACtB,sEAAsE;gBACtE,OAAO;YACT,KAAK/D,YAAYoE,SAAS;YAC1B,KAAKpE,YAAYmE,QAAQ;gBACvB,wEAAwE;gBACxE,uEAAuE;gBACvE,8BAA8B;gBAC9B,OAAO;YACT;gBACEmD;gBACA,OAAO;QACX;IACF;AACF;AAEA,MAAME,OAAO,KAAO;AAEpB,SAASJ,0BACPtF,IAAkB,EAClBwB,KAA+B,EAC/B5B,GAAW,EACX+F,OAAmD;IAEnD,sEAAsE;IACtEA,QAAQzG,IAAI,CAAC,CAAC0G;QACZ,IAAIA,cAAc,MAAM;YACtB,yEAAyE;YACzE,MAAMC,UAAUlH,yBAAyBqB,MAAMwB,OAAO5B;YACtDrB,mBAAmB+C,KAAKD,GAAG,IAAIwE,SAASD;QAC1C;IACF,GAAGF;AACL;AAEA,gFAAgF;AAChF,8EAA8E;AAC9E,6EAA6E;AAC7E,qEAAqE;AACrE,gFAAgF;AAEhF,SAASI,qBAAqBC,CAAe,EAAEC,CAAe;IAC5D,6EAA6E;IAC7E,wEAAwE;IACxE,UAAU;IAEV,sEAAsE;IACtE,MAAMC,eAAeD,EAAEjG,QAAQ,GAAGgG,EAAEhG,QAAQ;IAC5C,IAAIkG,iBAAiB,GAAG;QACtB,OAAOA;IACT;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,MAAMC,YAAYF,EAAE/F,KAAK,GAAG8F,EAAE9F,KAAK;IACnC,IAAIiG,cAAc,GAAG;QACnB,OAAOA;IACT;IAEA,0EAA0E;IAC1E,0EAA0E;IAC1E,OAAOF,EAAE7F,MAAM,GAAG4F,EAAE5F,MAAM;AAC5B;AAEA,SAASG,SAAS6F,IAAyB,EAAEC,IAAkB;IAC7D,MAAMC,QAAQF,KAAKG,MAAM;IACzBH,KAAKI,IAAI,CAACH;IACVA,KAAK/F,UAAU,GAAGgG;IAClBG,WAAWL,MAAMC,MAAMC;AACzB;AAEA,SAAS9E,SAAS4E,IAAyB;IACzC,OAAOA,KAAKG,MAAM,KAAK,IAAI,OAAOH,IAAI,CAAC,EAAE;AAC3C;AAEA,SAASxE,QAAQwE,IAAyB;IACxC,IAAIA,KAAKG,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IACA,MAAMG,QAAQN,IAAI,CAAC,EAAE;IACrBM,MAAMpG,UAAU,GAAG,CAAC;IACpB,MAAMqG,OAAOP,KAAKQ,GAAG;IACrB,IAAID,SAASD,OAAO;QAClBN,IAAI,CAAC,EAAE,GAAGO;QACVA,KAAKrG,UAAU,GAAG;QAClBuG,aAAaT,MAAMO,MAAM;IAC3B;IACA,OAAOD;AACT;AAEA,SAAShG,WAAW0F,IAAyB,EAAEC,IAAkB;IAC/D,MAAMC,QAAQD,KAAK/F,UAAU;IAC7B,IAAIgG,UAAU,CAAC,GAAG;QAChBD,KAAK/F,UAAU,GAAG,CAAC;QACnB,IAAI8F,KAAKG,MAAM,KAAK,GAAG;YACrB,MAAMI,OAAOP,KAAKQ,GAAG;YACrB,IAAID,SAASN,MAAM;gBACjBD,IAAI,CAACE,MAAM,GAAGK;gBACdA,KAAKrG,UAAU,GAAGgG;gBAClBO,aAAaT,MAAMO,MAAML;YAC3B;QACF;IACF;AACF;AAEA,SAAS1F,WAAWwF,IAAyB,EAAEC,IAAkB;IAC/D,MAAMC,QAAQD,KAAK/F,UAAU;IAC7B,IAAIgG,UAAU,CAAC,GAAG;QAChB,IAAIA,UAAU,GAAG;YACfO,aAAaT,MAAMC,MAAM;QAC3B,OAAO;YACL,MAAMS,cAAc,AAACR,QAAQ,MAAO;YACpC,MAAMS,SAASX,IAAI,CAACU,YAAY;YAChC,IAAIf,qBAAqBgB,QAAQV,QAAQ,GAAG;gBAC1C,iCAAiC;gBACjCI,WAAWL,MAAMC,MAAMC;YACzB,OAAO;gBACL,+CAA+C;gBAC/CO,aAAaT,MAAMC,MAAMC;YAC3B;QACF;IACF;AACF;AAEA,SAASG,WACPL,IAAyB,EACzBC,IAAkB,EAClBW,CAAS;IAET,IAAIV,QAAQU;IACZ,MAAOV,QAAQ,EAAG;QAChB,MAAMQ,cAAc,AAACR,QAAQ,MAAO;QACpC,MAAMS,SAASX,IAAI,CAACU,YAAY;QAChC,IAAIf,qBAAqBgB,QAAQV,QAAQ,GAAG;YAC1C,wCAAwC;YACxCD,IAAI,CAACU,YAAY,GAAGT;YACpBA,KAAK/F,UAAU,GAAGwG;YAClBV,IAAI,CAACE,MAAM,GAAGS;YACdA,OAAOzG,UAAU,GAAGgG;YAEpBA,QAAQQ;QACV,OAAO;YACL,+BAA+B;YAC/B;QACF;IACF;AACF;AAEA,SAASD,aACPT,IAAyB,EACzBC,IAAkB,EAClBW,CAAS;IAET,IAAIV,QAAQU;IACZ,MAAMT,SAASH,KAAKG,MAAM;IAC1B,MAAMU,aAAaV,WAAW;IAC9B,MAAOD,QAAQW,WAAY;QACzB,MAAMC,YAAY,AAACZ,CAAAA,QAAQ,CAAA,IAAK,IAAI;QACpC,MAAMa,OAAOf,IAAI,CAACc,UAAU;QAC5B,MAAME,aAAaF,YAAY;QAC/B,MAAMG,QAAQjB,IAAI,CAACgB,WAAW;QAE9B,wEAAwE;QACxE,IAAIrB,qBAAqBoB,MAAMd,QAAQ,GAAG;YACxC,IAAIe,aAAab,UAAUR,qBAAqBsB,OAAOF,QAAQ,GAAG;gBAChEf,IAAI,CAACE,MAAM,GAAGe;gBACdA,MAAM/G,UAAU,GAAGgG;gBACnBF,IAAI,CAACgB,WAAW,GAAGf;gBACnBA,KAAK/F,UAAU,GAAG8G;gBAElBd,QAAQc;YACV,OAAO;gBACLhB,IAAI,CAACE,MAAM,GAAGa;gBACdA,KAAK7G,UAAU,GAAGgG;gBAClBF,IAAI,CAACc,UAAU,GAAGb;gBAClBA,KAAK/F,UAAU,GAAG4G;gBAElBZ,QAAQY;YACV;QACF,OAAO,IAAIE,aAAab,UAAUR,qBAAqBsB,OAAOhB,QAAQ,GAAG;YACvED,IAAI,CAACE,MAAM,GAAGe;YACdA,MAAM/G,UAAU,GAAGgG;YACnBF,IAAI,CAACgB,WAAW,GAAGf;YACnBA,KAAK/F,UAAU,GAAG8G;YAElBd,QAAQc;QACV,OAAO;YACL,kCAAkC;YAClC;QACF;IACF;AACF"}