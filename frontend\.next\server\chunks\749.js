"use strict";exports.id=749,exports.ids=[749],exports.modules={11223:(e,t,l)=>{l.d(t,{q:()=>o});var n=l(43210);function r(e){return null}r.getCollectionNode=function*(e,t){var l;let{childItems:r,title:o,children:i}=e,s=e.title||e.children,u=e.textValue||("string"==typeof s?s:"")||e["aria-label"]||"";u||(null==t?void 0:t.suppressTextValueWarning)||console.warn("<Item> with non-plain text contents is unsupported by type to select for accessibility. Please add a `textValue` prop."),yield{type:"item",props:e,rendered:s,textValue:u,"aria-label":e["aria-label"],hasChildNodes:null!=(l=e).hasChildItems?l.hasChildItems:!!(l.childItems||l.title&&n.Children.count(l.children)>0),*childNodes(){if(r)for(let e of r)yield{type:"item",value:e};else if(o){let e=[];n.Children.forEach(i,t=>{e.push({type:"item",element:t})}),yield*e}}}};let o=r},31294:(e,t,l)=>{l.d(t,{p:()=>a});var n=l(87653),r=l(43006),o=l(66775),i=l(25381),s=l(58285),u=l(89121),c=l(43210);function a(e){let{selectionManager:t,key:l,ref:a,shouldSelectOnPressUp:y,shouldUseVirtualFocus:p,focus:h,isDisabled:v,onAction:g,allowsDifferentPressOrigin:m,linkBehavior:K="action"}=e,b=(0,o.rd)(),S=e=>{if("keyboard"===e.pointerType&&(0,n.N)(e))t.toggleSelection(l);else{if("none"===t.selectionMode)return;if(t.isLink(l)){if("selection"===K&&a.current){let n=t.getItemProps(l);b.open(a.current,e,n.href,n.routerOptions),t.setSelectedKeys(t.selectedKeys);return}else if("override"===K||"none"===K)return}"single"===t.selectionMode?t.isSelected(l)&&!t.disallowEmptySelection?t.toggleSelection(l):t.replaceSelection(l):e&&e.shiftKey?t.extendSelection(l):"toggle"===t.selectionBehavior||e&&((0,n.B)(e)||"touch"===e.pointerType||"virtual"===e.pointerType)?t.toggleSelection(l):t.replaceSelection(l)}};(0,c.useEffect)(()=>{l===t.focusedKey&&t.isFocused&&!p&&(h?h():document.activeElement!==a.current&&a.current&&(0,r.l)(a.current))},[a,l,t.focusedKey,t.childFocusStrategy,t.isFocused,p]),v=v||t.isDisabled(l);let E={};p||v?v&&(E.onMouseDown=e=>{e.preventDefault()}):E={tabIndex:l===t.focusedKey?0:-1,onFocus(e){e.target===a.current&&t.setFocusedKey(l)}};let T=t.isLink(l)&&"override"===K,w=t.isLink(l)&&"selection"!==K&&"none"!==K,N=!v&&t.canSelectItem(l)&&!T,k=(g||w)&&!v,R=k&&("replace"===t.selectionBehavior?!N:!N||t.isEmpty),x=k&&N&&"replace"===t.selectionBehavior,F=R||x,M=(0,c.useRef)(null),C=F&&N,I=(0,c.useRef)(!1),L=(0,c.useRef)(!1),D=e=>{if(g&&g(),w&&a.current){let n=t.getItemProps(l);b.open(a.current,e,n.href,n.routerOptions)}},P={};y?(P.onPressStart=e=>{M.current=e.pointerType,I.current=C,"keyboard"===e.pointerType&&(!F||f())&&S(e)},m?(P.onPressUp=R?void 0:e=>{"keyboard"!==e.pointerType&&N&&S(e)},P.onPress=R?D:void 0):P.onPress=e=>{R||x&&"mouse"!==e.pointerType?("keyboard"!==e.pointerType||d())&&D(e):"keyboard"!==e.pointerType&&N&&S(e)}):(P.onPressStart=e=>{M.current=e.pointerType,I.current=C,L.current=R,N&&("mouse"===e.pointerType&&!R||"keyboard"===e.pointerType&&(!k||f()))&&S(e)},P.onPress=e=>{("touch"===e.pointerType||"pen"===e.pointerType||"virtual"===e.pointerType||"keyboard"===e.pointerType&&F&&d()||"mouse"===e.pointerType&&L.current)&&(F?D(e):N&&S(e))}),E["data-key"]=l,P.preventFocusOnPress=p;let{pressProps:A,isPressed:B}=(0,s.d)(P),O=x?e=>{"mouse"===M.current&&(e.stopPropagation(),e.preventDefault(),D(e))}:void 0,{longPressProps:V}=(0,u.H)({isDisabled:!C,onLongPress(e){"touch"===e.pointerType&&(S(e),t.setSelectionBehavior("toggle"))}}),$=t.isLink(l)?e=>{o.Fe.isOpening||e.preventDefault()}:void 0;return{itemProps:(0,i.v)(E,N||R?A:{},C?V:{},{onDoubleClick:O,onDragStartCapture:e=>{"touch"===M.current&&I.current&&e.preventDefault()},onClick:$}),isPressed:B,isSelected:t.isSelected(l),isFocused:t.isFocused&&t.focusedKey===l,isDisabled:v,allowsSelection:N,hasAction:F}}function d(){let e=window.event;return(null==e?void 0:e.key)==="Enter"}function f(){let e=window.event;return(null==e?void 0:e.key)===" "||(null==e?void 0:e.code)==="Space"}},35356:(e,t,l)=>{l.d(t,{o:()=>n});function n(e,t){if(!e)return!1;let l=window.getComputedStyle(e),n=/(auto|scroll)/.test(l.overflow+l.overflowX+l.overflowY);return n&&t&&(n=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),n}},38663:(e,t,l)=>{l.d(t,{I:()=>r});var n=l(43210);function r(e){let{keyboardDelegate:t,selectionManager:l,onTypeSelect:r}=e,o=(0,n.useRef)({search:"",timeout:void 0}).current;return{typeSelectProps:{onKeyDownCapture:t.getKeyForSearch?e=>{var n;let i=1!==(n=e.key).length&&/^[A-Z]/i.test(n)?"":n;if(i&&!e.ctrlKey&&!e.metaKey&&e.currentTarget.contains(e.target)){if(" "===i&&o.search.trim().length>0&&(e.preventDefault(),"continuePropagation"in e||e.stopPropagation()),o.search+=i,null!=t.getKeyForSearch){let e=t.getKeyForSearch(o.search,l.focusedKey);null==e&&(e=t.getKeyForSearch(o.search)),null!=e&&(l.setFocusedKey(e),r&&r(e))}clearTimeout(o.timeout),o.timeout=setTimeout(()=>{o.search=""},1e3)}}:void 0}}}},39297:(e,t,l)=>{l.d(t,{cn:()=>i});var n=l(83451);let r=function(){for(var e,t,l=0,n="";l<arguments.length;)(e=arguments[l++])&&(t=function e(t){var l,n,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t))for(l=0;l<t.length;l++)t[l]&&(n=e(t[l]))&&(r&&(r+=" "),r+=n);else for(l in t)t[l]&&(r&&(r+=" "),r+=l);return r}(e))&&(n&&(n+=" "),n+=t);return n};var o=(0,l(82348).zu)({extend:n.w});function i(...e){return o(r(e))}},73469:(e,t,l)=>{l.d(t,{p:()=>y});class n{*[Symbol.iterator](){yield*this.iterable}get size(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(e){var t;let l=this.keyMap.get(e);return l&&null!=(t=l.prevKey)?t:null}getKeyAfter(e){var t;let l=this.keyMap.get(e);return l&&null!=(t=l.nextKey)?t:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(e){var t;return null!=(t=this.keyMap.get(e))?t:null}at(e){let t=[...this.getKeys()];return this.getItem(t[e])}getChildren(e){let t=this.keyMap.get(e);return(null==t?void 0:t.childNodes)||[]}constructor(e){var t;this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=e;let l=e=>{if(this.keyMap.set(e.key,e),e.childNodes&&"section"===e.type)for(let t of e.childNodes)l(t)};for(let t of e)l(t);let n=null,r=0;for(let[e,t]of this.keyMap)n?(n.nextKey=e,t.prevKey=n.key):(this.firstKey=e,t.prevKey=void 0),"item"===t.type&&(t.index=r++),(n=t).nextKey=void 0;this.lastKey=null!=(t=null==n?void 0:n.key)?t:null}}class r extends Set{constructor(e,t,l){super(e),e instanceof r?(this.anchorKey=null!=t?t:e.anchorKey,this.currentKey=null!=l?l:e.currentKey):(this.anchorKey=null!=t?t:null,this.currentKey=null!=l?l:null)}}var o=l(43210);function i(e,t){return e?"all"===e?"all":new r(e):t}function s(e,t,l){if(t.parentKey===l.parentKey)return t.index-l.index;let n=[...u(e,t),t],r=[...u(e,l),l],o=n.slice(0,r.length).findIndex((e,t)=>e!==r[t]);return -1!==o?(t=n[o],l=r[o],t.index-l.index):n.findIndex(e=>e===l)>=0?1:(r.findIndex(e=>e===t),-1)}function u(e,t){let l=[],n=t;for(;(null==n?void 0:n.parentKey)!=null;)(n=e.getItem(n.parentKey))&&l.unshift(n);return l}class c{get selectionMode(){return this.state.selectionMode}get disallowEmptySelection(){return this.state.disallowEmptySelection}get selectionBehavior(){return this.state.selectionBehavior}setSelectionBehavior(e){this.state.setSelectionBehavior(e)}get isFocused(){return this.state.isFocused}setFocused(e){this.state.setFocused(e)}get focusedKey(){return this.state.focusedKey}get childFocusStrategy(){return this.state.childFocusStrategy}setFocusedKey(e,t){(null==e||this.collection.getItem(e))&&this.state.setFocusedKey(e,t)}get selectedKeys(){return"all"===this.state.selectedKeys?new Set(this.getSelectAllKeys()):this.state.selectedKeys}get rawSelection(){return this.state.selectedKeys}isSelected(e){if("none"===this.state.selectionMode)return!1;let t=this.getKey(e);return null!=t&&("all"===this.state.selectedKeys?this.canSelectItem(t):this.state.selectedKeys.has(t))}get isEmpty(){return"all"!==this.state.selectedKeys&&0===this.state.selectedKeys.size}get isSelectAll(){if(this.isEmpty)return!1;if("all"===this.state.selectedKeys)return!0;if(null!=this._isSelectAll)return this._isSelectAll;let e=this.getSelectAllKeys(),t=this.state.selectedKeys;return this._isSelectAll=e.every(e=>t.has(e)),this._isSelectAll}get firstSelectedKey(){var e;let t=null;for(let e of this.state.selectedKeys){let l=this.collection.getItem(e);(!t||l&&0>s(this.collection,l,t))&&(t=l)}return null!=(e=null==t?void 0:t.key)?e:null}get lastSelectedKey(){var e;let t=null;for(let e of this.state.selectedKeys){let l=this.collection.getItem(e);(!t||l&&s(this.collection,l,t)>0)&&(t=l)}return null!=(e=null==t?void 0:t.key)?e:null}get disabledKeys(){return this.state.disabledKeys}get disabledBehavior(){return this.state.disabledBehavior}extendSelection(e){let t;if("none"===this.selectionMode)return;if("single"===this.selectionMode)return void this.replaceSelection(e);let l=this.getKey(e);if(null!=l){if("all"===this.state.selectedKeys)t=new r([l],l,l);else{var n,o;let e=this.state.selectedKeys,i=null!=(n=e.anchorKey)?n:l;for(let n of(t=new r(e,i,l),this.getKeyRange(i,null!=(o=e.currentKey)?o:l)))t.delete(n);for(let e of this.getKeyRange(l,i))this.canSelectItem(e)&&t.add(e)}this.state.setSelectedKeys(t)}}getKeyRange(e,t){let l=this.collection.getItem(e),n=this.collection.getItem(t);return l&&n?0>=s(this.collection,l,n)?this.getKeyRangeInternal(e,t):this.getKeyRangeInternal(t,e):[]}getKeyRangeInternal(e,t){var l;if(null==(l=this.layoutDelegate)?void 0:l.getKeyRange)return this.layoutDelegate.getKeyRange(e,t);let n=[],r=e;for(;null!=r;){let e=this.collection.getItem(r);if(e&&("item"===e.type||"cell"===e.type&&this.allowsCellSelection)&&n.push(r),r===t)return n;r=this.collection.getKeyAfter(r)}return[]}getKey(e){let t=this.collection.getItem(e);if(!t||"cell"===t.type&&this.allowsCellSelection)return e;for(;t&&"item"!==t.type&&null!=t.parentKey;)t=this.collection.getItem(t.parentKey);return t&&"item"===t.type?t.key:null}toggleSelection(e){if("none"===this.selectionMode)return;if("single"===this.selectionMode&&!this.isSelected(e))return void this.replaceSelection(e);let t=this.getKey(e);if(null==t)return;let l=new r("all"===this.state.selectedKeys?this.getSelectAllKeys():this.state.selectedKeys);l.has(t)?l.delete(t):this.canSelectItem(t)&&(l.add(t),l.anchorKey=t,l.currentKey=t),this.disallowEmptySelection&&0===l.size||this.state.setSelectedKeys(l)}replaceSelection(e){if("none"===this.selectionMode)return;let t=this.getKey(e);if(null==t)return;let l=this.canSelectItem(t)?new r([t],t,t):new r;this.state.setSelectedKeys(l)}setSelectedKeys(e){if("none"===this.selectionMode)return;let t=new r;for(let l of e){let e=this.getKey(l);if(null!=e&&(t.add(e),"single"===this.selectionMode))break}this.state.setSelectedKeys(t)}getSelectAllKeys(){let e=[],t=l=>{for(;null!=l;){if(this.canSelectItem(l)){var n,r,o;let i=this.collection.getItem(l);(null==i?void 0:i.type)==="item"&&e.push(l),(null==i?void 0:i.hasChildNodes)&&(this.allowsCellSelection||"item"!==i.type)&&t(null!=(r=null==(n=function(e,t){!1;let l=0;for(let n of e){if(l===t)return n;l++}}("function"==typeof(o=this.collection).getChildren?o.getChildren(i.key):i.childNodes,0))?void 0:n.key)?r:null)}l=this.collection.getKeyAfter(l)}};return t(this.collection.getFirstKey()),e}selectAll(){this.isSelectAll||"multiple"!==this.selectionMode||this.state.setSelectedKeys("all")}clearSelection(){!this.disallowEmptySelection&&("all"===this.state.selectedKeys||this.state.selectedKeys.size>0)&&this.state.setSelectedKeys(new r)}toggleSelectAll(){this.isSelectAll?this.clearSelection():this.selectAll()}select(e,t){"none"!==this.selectionMode&&("single"===this.selectionMode?this.isSelected(e)&&!this.disallowEmptySelection?this.toggleSelection(e):this.replaceSelection(e):"toggle"===this.selectionBehavior||t&&("touch"===t.pointerType||"virtual"===t.pointerType)?this.toggleSelection(e):this.replaceSelection(e))}isSelectionEqual(e){if(e===this.state.selectedKeys)return!0;let t=this.selectedKeys;if(e.size!==t.size)return!1;for(let l of e)if(!t.has(l))return!1;for(let l of t)if(!e.has(l))return!1;return!0}canSelectItem(e){var t;if("none"===this.state.selectionMode||this.state.disabledKeys.has(e))return!1;let l=this.collection.getItem(e);return!!l&&(null==l||null==(t=l.props)||!t.isDisabled)&&("cell"!==l.type||!!this.allowsCellSelection)}isDisabled(e){var t,l;return"all"===this.state.disabledBehavior&&(this.state.disabledKeys.has(e)||!!(null==(l=this.collection.getItem(e))||null==(t=l.props)?void 0:t.isDisabled))}isLink(e){var t,l;return!!(null==(l=this.collection.getItem(e))||null==(t=l.props)?void 0:t.href)}getItemProps(e){var t;return null==(t=this.collection.getItem(e))?void 0:t.props}withCollection(e){return new c(e,this.state,{allowsCellSelection:this.allowsCellSelection,layoutDelegate:this.layoutDelegate||void 0})}constructor(e,t,l){var n;this.collection=e,this.state=t,this.allowsCellSelection=null!=(n=null==l?void 0:l.allowsCellSelection)&&n,this._isSelectAll=null,this.layoutDelegate=(null==l?void 0:l.layoutDelegate)||null}}class a{build(e,t){return this.context=t,d(()=>this.iterateCollection(e))}*iterateCollection(e){let{children:t,items:l}=e;if(o.isValidElement(t)&&t.type===o.Fragment)yield*this.iterateCollection({children:t.props.children,items:l});else if("function"==typeof t){if(!l)throw Error("props.children was a function but props.items is missing");let e=0;for(let n of l)yield*this.getFullNode({value:n,index:e},{renderer:t}),e++}else{let e=[];o.Children.forEach(t,t=>{t&&e.push(t)});let l=0;for(let t of e)for(let e of this.getFullNode({element:t,index:l},{}))l++,yield e}}getKey(e,t,l,n){if(null!=e.key)return e.key;if("cell"===t.type&&null!=t.key)return`${n}${t.key}`;let r=t.value;if(null!=r){var o;let e=null!=(o=r.key)?o:r.id;if(null==e)throw Error("No key found for item");return e}return n?`${n}.${t.index}`:`$.${t.index}`}getChildState(e,t){return{renderer:t.renderer||e.renderer}}*getFullNode(e,t,l,n){var r,i,s,u,c,a,y,p;if(o.isValidElement(e.element)&&e.element.type===o.Fragment){let i=[];o.Children.forEach(e.element.props.children,e=>{i.push(e)});let s=null!=(r=e.index)?r:0;for(let e of i)yield*this.getFullNode({element:e,index:s++},t,l,n);return}let h=e.element;if(!h&&e.value&&t&&t.renderer){let l=this.cache.get(e.value);if(l&&(!l.shouldInvalidate||!l.shouldInvalidate(this.context))){l.index=e.index,l.parentKey=n?n.key:null,yield l;return}h=t.renderer(e.value)}if(o.isValidElement(h)){let r=h.type;if("function"!=typeof r&&"function"!=typeof r.getCollectionNode){let e=h.type;throw Error(`Unknown element <${e}> in collection.`)}let o=r.getCollectionNode(h.props,this.context),d=null!=(i=e.index)?i:0,y=o.next();for(;!y.done&&y.value;){let r=y.value;e.index=d;let i=null!=(s=r.key)?s:null;null==i&&(i=r.element?null:this.getKey(h,e,t,l));let p=[...this.getFullNode({...r,key:i,index:d,wrapper:function(e,t){return e&&t?l=>e(t(l)):e||t||void 0}(e.wrapper,r.wrapper)},this.getChildState(t,r),l?`${l}${h.key}`:h.key,n)];for(let t of p){if(t.value=null!=(c=null!=(u=r.value)?u:e.value)?c:null,t.value&&this.cache.set(t.value,t),e.type&&t.type!==e.type)throw Error(`Unsupported type <${f(t.type)}> in <${f(null!=(a=null==n?void 0:n.type)?a:"unknown parent type")}>. Only <${f(e.type)}> is supported.`);d++,yield t}y=o.next(p)}return}if(null==e.key||null==e.type)return;let v=this,g={type:e.type,props:e.props,key:e.key,parentKey:n?n.key:null,value:null!=(y=e.value)?y:null,level:n?n.level+1:0,index:e.index,rendered:e.rendered,textValue:null!=(p=e.textValue)?p:"","aria-label":e["aria-label"],wrapper:e.wrapper,shouldInvalidate:e.shouldInvalidate,hasChildNodes:e.hasChildNodes||!1,childNodes:d(function*(){if(!e.hasChildNodes||!e.childNodes)return;let l=0;for(let n of e.childNodes())for(let e of(null!=n.key&&(n.key=`${g.key}${n.key}`),v.getFullNode({...n,index:l},v.getChildState(t,n),g.key,g)))l++,yield e})};yield g}constructor(){this.cache=new WeakMap}}function d(e){let t=[],l=null;return{*[Symbol.iterator](){for(let e of t)yield e;for(let n of(l||(l=e()),l))t.push(n),yield n}}}function f(e){return e[0].toUpperCase()+e.slice(1)}function y(e){let{filter:t,layoutDelegate:l}=e,s=function(e){let{selectionMode:t="none",disallowEmptySelection:l=!1,allowDuplicateSelectionEvents:n,selectionBehavior:s="toggle",disabledBehavior:u="all"}=e,c=(0,o.useRef)(!1),[,a]=(0,o.useState)(!1),d=(0,o.useRef)(null),f=(0,o.useRef)(null),[,y]=(0,o.useState)(null),[p,h]=function(e,t,l){let[n,r]=(0,o.useState)(e||t),i=(0,o.useRef)(void 0!==e),s=void 0!==e;(0,o.useEffect)(()=>{i.current,i.current=s},[s]);let u=s?e:n,c=(0,o.useCallback)((e,...t)=>{let n=(e,...t)=>{l&&!Object.is(u,e)&&l(e,...t),s||(u=e)};"function"==typeof e?r((l,...r)=>{let o=e(s?u:l,...r);return(n(o,...t),s)?l:o}):(s||r(e),n(e,...t))},[s,u,l]);return[u,c]}((0,o.useMemo)(()=>i(e.selectedKeys),[e.selectedKeys]),(0,o.useMemo)(()=>i(e.defaultSelectedKeys,new r),[e.defaultSelectedKeys]),e.onSelectionChange),v=(0,o.useMemo)(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),[g,m]=(0,o.useState)(s);"replace"===s&&"toggle"===g&&"object"==typeof p&&0===p.size&&m("replace");let K=(0,o.useRef)(s);return(0,o.useEffect)(()=>{s!==K.current&&(m(s),K.current=s)},[s]),{selectionMode:t,disallowEmptySelection:l,selectionBehavior:g,setSelectionBehavior:m,get isFocused(){return c.current},setFocused(e){c.current=e,a(e)},get focusedKey(){return d.current},get childFocusStrategy(){return f.current},setFocusedKey(e,t="first"){d.current=e,f.current=t,y(e)},selectedKeys:p,setSelectedKeys(e){(n||!function(e,t){if(e.size!==t.size)return!1;for(let l of e)if(!t.has(l))return!1;return!0}(e,p))&&h(e)},disabledKeys:v,disabledBehavior:u}}(e),u=(0,o.useMemo)(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),d=(0,o.useCallback)(e=>new n(t?t(e):e),[t]),f=(0,o.useMemo)(()=>({suppressTextValueWarning:e.suppressTextValueWarning}),[e.suppressTextValueWarning]),y=function(e,t,l){let n=(0,o.useMemo)(()=>new a,[]),{children:r,items:i,collection:s}=e;return(0,o.useMemo)(()=>s||t(n.build({children:r,items:i},l)),[n,r,i,s,l,t])}(e,d,f),p=(0,o.useMemo)(()=>new c(y,s,{layoutDelegate:l}),[y,s,l]),h=(0,o.useRef)(null);return(0,o.useEffect)(()=>{if(null!=s.focusedKey&&!y.getItem(s.focusedKey)&&h.current){var e,t,l,n,r,o,i;let u=h.current.getItem(s.focusedKey),c=[...h.current.getKeys()].map(e=>{let t=h.current.getItem(e);return(null==t?void 0:t.type)==="item"?t:null}).filter(e=>null!==e),a=[...y.getKeys()].map(e=>{let t=y.getItem(e);return(null==t?void 0:t.type)==="item"?t:null}).filter(e=>null!==e),d=(null!=(e=null==c?void 0:c.length)?e:0)-(null!=(t=null==a?void 0:a.length)?t:0),f=Math.min(d>1?Math.max((null!=(l=null==u?void 0:u.index)?l:0)-d+1,0):null!=(n=null==u?void 0:u.index)?n:0,(null!=(r=null==a?void 0:a.length)?r:0)-1),v=null,g=!1;for(;f>=0;){if(!p.isDisabled(a[f].key)){v=a[f];break}f<a.length-1&&!g?f++:(g=!0,f>(null!=(o=null==u?void 0:u.index)?o:0)&&(f=null!=(i=null==u?void 0:u.index)?i:0),f--)}s.setFocusedKey(v?v.key:null)}h.current=y},[y,p,s,s.focusedKey]),{collection:y,disabledKeys:u,selectionManager:p}}},87653:(e,t,l)=>{l.d(t,{B:()=>o,N:()=>r});var n=l(53570);function r(e){return(0,n.lg)()?e.altKey:e.ctrlKey}function o(e){return(0,n.cX)()?e.metaKey:e.ctrlKey}},89121:(e,t,l)=>{l.d(t,{H:()=>a});var n=l(58285),r=l(35392),o=l(7717),i=l(43210);let s=0,u=new Map;var c=l(25381);function a(e){let{isDisabled:t,onLongPressStart:l,onLongPressEnd:a,onLongPress:d,threshold:f=500,accessibilityDescription:y}=e,p=(0,i.useRef)(void 0),{addGlobalListener:h,removeGlobalListener:v}=(0,r.A)(),{pressProps:g}=(0,n.d)({isDisabled:t,onPressStart(e){if(e.continuePropagation(),("mouse"===e.pointerType||"touch"===e.pointerType)&&(l&&l({...e,type:"longpressstart"}),p.current=setTimeout(()=>{e.target.dispatchEvent(new PointerEvent("pointercancel",{bubbles:!0})),d&&d({...e,type:"longpress"}),p.current=void 0},f),"touch"===e.pointerType)){let t=e=>{e.preventDefault()};h(e.target,"contextmenu",t,{once:!0}),h(window,"pointerup",()=>{setTimeout(()=>{v(e.target,"contextmenu",t)},30)},{once:!0})}},onPressEnd(e){p.current&&clearTimeout(p.current),a&&("mouse"===e.pointerType||"touch"===e.pointerType)&&a({...e,type:"longpressend"})}}),m=function(e){let[t,l]=(0,i.useState)();return(0,o.N)(()=>{if(!e)return;let t=u.get(e);if(t)l(t.element.id);else{let n=`react-aria-description-${s++}`;l(n);let r=document.createElement("div");r.id=n,r.style.display="none",r.textContent=e,document.body.appendChild(r),t={refCount:0,element:r},u.set(e,t)}return t.refCount++,()=>{t&&0==--t.refCount&&(t.element.remove(),u.delete(e))}},[e]),{"aria-describedby":e?t:void 0}}(d&&!t?y:void 0);return{longPressProps:(0,c.v)(g,m)}}},92e3:(e,t,l)=>{l.d(t,{y:()=>b});var n=l(87653),r=l(38663),o=l(51215),i=l(43210),s=l(97838),u=l(43006),c=l(66775),a=l(29920),d=l(50509);function f(e,t,l,n){let r=(0,d.J)(l),o=null==l;(0,i.useEffect)(()=>{if(o||!e.current)return;let l=e.current;return l.addEventListener(t,r,n),()=>{l.removeEventListener(t,r,n)}},[e,t,n,o,r])}var y=l(35356);function p(e,t){let l=h(e,t,"left"),n=h(e,t,"top"),r=t.offsetWidth,o=t.offsetHeight,i=e.scrollLeft,s=e.scrollTop,{borderTopWidth:u,borderLeftWidth:c}=getComputedStyle(e),a=e.scrollLeft+parseInt(c,10),d=e.scrollTop+parseInt(u,10),f=a+e.clientWidth,y=d+e.clientHeight;l<=i?i=l-parseInt(c,10):l+r>f&&(i+=l+r-f),n<=d?s=n-parseInt(u,10):n+o>y&&(s+=n+o-y),e.scrollLeft=i,e.scrollTop=s}function h(e,t,l){let n="left"===l?"offsetLeft":"offsetTop",r=0;for(;t.offsetParent&&(r+=t[n],t.offsetParent!==e);){if(t.offsetParent.contains(e)){r-=e[n];break}t=t.offsetParent}return r}function v(e,t){if(e&&document.contains(e)){let i=document.scrollingElement||document.documentElement;if("hidden"===window.getComputedStyle(i).overflow)for(let t of function(e,t){let l=[];for(;e&&e!==document.documentElement;)(0,y.o)(e,void 0)&&l.push(e),e=e.parentElement;return l}(e))p(t,e);else{var l,n,r,o;let{left:i,top:s}=e.getBoundingClientRect();null==e||null==(l=e.scrollIntoView)||l.call(e,{block:"nearest"});let{left:u,top:c}=e.getBoundingClientRect();(Math.abs(i-u)>1||Math.abs(s-c)>1)&&(null==t||null==(r=t.containingElement)||null==(n=r.scrollIntoView)||n.call(r,{block:"center",inline:"center"}),null==(o=e.scrollIntoView)||o.call(e,{block:"nearest"}))}}}var g=l(25381),m=l(89130),K=l(30900);function b(e){let t,{selectionManager:l,keyboardDelegate:d,ref:y,autoFocus:h=!1,shouldFocusWrap:b=!1,disallowEmptySelection:S=!1,disallowSelectAll:E=!1,selectOnFocus:T="replace"===l.selectionBehavior,disallowTypeAhead:w=!1,shouldUseVirtualFocus:N,allowsTabNavigation:k=!1,isVirtualized:R,scrollRef:x=y,linkBehavior:F="action"}=e,{direction:M}=(0,K.Y)(),C=(0,c.rd)(),I=(0,i.useRef)({top:0,left:0});f(x,"scroll",R?void 0:()=>{var e,t,l,n;I.current={top:null!=(l=null==(e=x.current)?void 0:e.scrollTop)?l:0,left:null!=(n=null==(t=x.current)?void 0:t.scrollLeft)?n:0}});let L=(0,i.useRef)(h);(0,i.useEffect)(()=>{if(L.current){var e,t,n,r;let o=null;"first"===h&&(o=null!=(n=null==(e=d.getFirstKey)?void 0:e.call(d))?n:null),"last"===h&&(o=null!=(r=null==(t=d.getLastKey)?void 0:t.call(d))?r:null);let i=l.selectedKeys;if(i.size){for(let e of i)if(l.canSelectItem(e)){o=e;break}}l.setFocused(!0),l.setFocusedKey(o),null==o&&!N&&y.current&&(0,u.l)(y.current)}},[]);let D=(0,i.useRef)(l.focusedKey);(0,i.useEffect)(()=>{if(l.isFocused&&null!=l.focusedKey&&(l.focusedKey!==D.current||L.current)&&x.current&&y.current){let e=(0,m.ME)(),t=y.current.querySelector(`[data-key="${CSS.escape(l.focusedKey.toString())}"]`);if(!t)return;("keyboard"===e||L.current)&&(p(x.current,t),"virtual"!==e&&v(t,{containingElement:y.current}))}!N&&l.isFocused&&null==l.focusedKey&&null!=D.current&&y.current&&(0,u.l)(y.current),D.current=l.focusedKey,L.current=!1}),f(y,"react-aria-focus-scope-restore",e=>{e.preventDefault(),l.setFocused(!0)});let P={onKeyDown:e=>{var t,r,i,u,c,f,p,h,v,g,m,K,w;if(e.altKey&&"Tab"===e.key&&e.preventDefault(),!(null==(t=y.current)?void 0:t.contains(e.target)))return;let N=(t,r)=>{if(null!=t){if(l.isLink(t)&&"selection"===F&&T&&!(0,n.N)(e)){var i;(0,o.flushSync)(()=>{l.setFocusedKey(t,r)});let n=null==(i=x.current)?void 0:i.querySelector(`[data-key="${CSS.escape(t.toString())}"]`),s=l.getItemProps(t);n&&C.open(n,e,s.href,s.routerOptions);return}l.setFocusedKey(t,r),l.isLink(t)&&"override"===F||(e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):T&&!(0,n.N)(e)&&l.replaceSelection(t))}};switch(e.key){case"ArrowDown":if(d.getKeyBelow){let t=null!=l.focusedKey?null==(r=d.getKeyBelow)?void 0:r.call(d,l.focusedKey):null==(i=d.getFirstKey)?void 0:i.call(d);null==t&&b&&(t=null==(u=d.getFirstKey)?void 0:u.call(d,l.focusedKey)),null!=t&&(e.preventDefault(),N(t))}break;case"ArrowUp":if(d.getKeyAbove){let t=null!=l.focusedKey?null==(c=d.getKeyAbove)?void 0:c.call(d,l.focusedKey):null==(f=d.getLastKey)?void 0:f.call(d);null==t&&b&&(t=null==(p=d.getLastKey)?void 0:p.call(d,l.focusedKey)),null!=t&&(e.preventDefault(),N(t))}break;case"ArrowLeft":if(d.getKeyLeftOf){let t=null!=l.focusedKey?null==(h=d.getKeyLeftOf)?void 0:h.call(d,l.focusedKey):null;null==t&&b&&(t="rtl"===M?null==(v=d.getFirstKey)?void 0:v.call(d,l.focusedKey):null==(g=d.getLastKey)?void 0:g.call(d,l.focusedKey)),null!=t&&(e.preventDefault(),N(t,"rtl"===M?"first":"last"))}break;case"ArrowRight":if(d.getKeyRightOf){let t=null!=l.focusedKey?null==(m=d.getKeyRightOf)?void 0:m.call(d,l.focusedKey):null;null==t&&b&&(t="rtl"===M?null==(K=d.getLastKey)?void 0:K.call(d,l.focusedKey):null==(w=d.getFirstKey)?void 0:w.call(d,l.focusedKey)),null!=t&&(e.preventDefault(),N(t,"rtl"===M?"last":"first"))}break;case"Home":if(d.getFirstKey){e.preventDefault();let t=d.getFirstKey(l.focusedKey,(0,n.B)(e));l.setFocusedKey(t),null!=t&&((0,n.B)(e)&&e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):T&&l.replaceSelection(t))}break;case"End":if(d.getLastKey){e.preventDefault();let t=d.getLastKey(l.focusedKey,(0,n.B)(e));l.setFocusedKey(t),null!=t&&((0,n.B)(e)&&e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):T&&l.replaceSelection(t))}break;case"PageDown":if(d.getKeyPageBelow&&null!=l.focusedKey){let t=d.getKeyPageBelow(l.focusedKey);null!=t&&(e.preventDefault(),N(t))}break;case"PageUp":if(d.getKeyPageAbove&&null!=l.focusedKey){let t=d.getKeyPageAbove(l.focusedKey);null!=t&&(e.preventDefault(),N(t))}break;case"a":(0,n.B)(e)&&"multiple"===l.selectionMode&&!0!==E&&(e.preventDefault(),l.selectAll());break;case"Escape":S||0===l.selectedKeys.size||(e.stopPropagation(),e.preventDefault(),l.clearSelection());break;case"Tab":if(!k)if(e.shiftKey)y.current.focus();else{let e,t,l=(0,s.N$)(y.current,{tabbable:!0});do(t=l.lastChild())&&(e=t);while(t);e&&!e.contains(document.activeElement)&&(0,a.e)(e)}}},onFocus:e=>{if(l.isFocused){e.currentTarget.contains(e.target)||l.setFocused(!1);return}if(e.currentTarget.contains(e.target)){if(l.setFocused(!0),null==l.focusedKey){var t,n,r,o;let i=e=>{null!=e&&(l.setFocusedKey(e),T&&l.replaceSelection(e))},s=e.relatedTarget;s&&e.currentTarget.compareDocumentPosition(s)&Node.DOCUMENT_POSITION_FOLLOWING?i(null!=(r=l.lastSelectedKey)?r:null==(t=d.getLastKey)?void 0:t.call(d)):i(null!=(o=l.firstSelectedKey)?o:null==(n=d.getFirstKey)?void 0:n.call(d))}else!R&&x.current&&(x.current.scrollTop=I.current.top,x.current.scrollLeft=I.current.left);if(null!=l.focusedKey&&x.current){let e=x.current.querySelector(`[data-key="${CSS.escape(l.focusedKey.toString())}"]`);e&&(e.contains(document.activeElement)||(0,a.e)(e),"keyboard"===(0,m.ME)()&&v(e,{containingElement:y.current}))}}},onBlur:e=>{e.currentTarget.contains(e.relatedTarget)||l.setFocused(!1)},onMouseDown(e){x.current===e.target&&e.preventDefault()}},{typeSelectProps:A}=(0,r.I)({keyboardDelegate:d,selectionManager:l});return w||(P=(0,g.v)(A,P)),N||(t=null==l.focusedKey?0:-1),{collectionProps:{...P,tabIndex:t}}}},97838:(e,t,l)=>{l.d(t,{n1:()=>a,N$:()=>N,Pu:()=>m});var n=l(43006),r=l(31272),o=l(7717),i=l(43210);let s=i.createContext(null),u="react-aria-focus-scope-restore",c=null;function a(e){var t,l,n,a,d;let f,y,{children:m,contain:k,restoreFocus:F,autoFocus:M}=e,C=(0,i.useRef)(null),I=(0,i.useRef)(null),L=(0,i.useRef)([]),{parentNode:D}=(0,i.useContext)(s)||{},P=(0,i.useMemo)(()=>new R({scopeRef:L}),[L]);(0,o.N)(()=>{let e=D||x.root;if(x.getTreeNode(e.scopeRef)&&c&&!K(c,e.scopeRef)){let t=x.getTreeNode(c);t&&(e=t)}e.addChild(P),x.addNode(P)},[P,D]),(0,o.N)(()=>{let e=x.getTreeNode(L);e&&(e.contain=!!k)},[k]),(0,o.N)(()=>{var e;let t=null==(e=C.current)?void 0:e.nextSibling,l=[],n=e=>e.stopPropagation();for(;t&&t!==I.current;)l.push(t),t.addEventListener(u,n),t=t.nextSibling;return L.current=l,()=>{for(let e of l)e.removeEventListener(u,n)}},[m]),t=L,l=F,n=k,(0,o.N)(()=>{if(l||n)return;let e=t.current,o=(0,r.T)(e?e[0]:void 0),i=e=>{let l=e.target;v(l,t.current)?c=t:g(l)||(c=null)};return o.addEventListener("focusin",i,!1),null==e||e.forEach(e=>e.addEventListener("focusin",i,!1)),()=>{o.removeEventListener("focusin",i,!1),null==e||e.forEach(e=>e.removeEventListener("focusin",i,!1))}},[t,l,n]),a=L,d=k,f=(0,i.useRef)(void 0),y=(0,i.useRef)(void 0),(0,o.N)(()=>{let e=a.current;if(!d){y.current&&(cancelAnimationFrame(y.current),y.current=void 0);return}let t=(0,r.T)(e?e[0]:void 0),l=e=>{if("Tab"!==e.key||e.altKey||e.ctrlKey||e.metaKey||!h(a)||e.isComposing)return;let l=t.activeElement,n=a.current;if(!n||!v(l,n))return;let r=N(p(n),{tabbable:!0},n);if(!l)return;r.currentNode=l;let o=e.shiftKey?r.previousNode():r.nextNode();o||(r.currentNode=e.shiftKey?n[n.length-1].nextElementSibling:n[0].previousElementSibling,o=e.shiftKey?r.previousNode():r.nextNode()),e.preventDefault(),o&&b(o,!0)},n=e=>{(!c||K(c,a))&&v(e.target,a.current)?(c=a,f.current=e.target):h(a)&&!g(e.target,a)?f.current?f.current.focus():c&&c.current&&E(c.current):h(a)&&(f.current=e.target)},o=e=>{y.current&&cancelAnimationFrame(y.current),y.current=requestAnimationFrame(()=>{if(t.activeElement&&h(a)&&!g(t.activeElement,a))if(c=a,t.body.contains(e.target)){var l;f.current=e.target,null==(l=f.current)||l.focus()}else c.current&&E(c.current)})};return t.addEventListener("keydown",l,!1),t.addEventListener("focusin",n,!1),null==e||e.forEach(e=>e.addEventListener("focusin",n,!1)),null==e||e.forEach(e=>e.addEventListener("focusout",o,!1)),()=>{t.removeEventListener("keydown",l,!1),t.removeEventListener("focusin",n,!1),null==e||e.forEach(e=>e.removeEventListener("focusin",n,!1)),null==e||e.forEach(e=>e.removeEventListener("focusout",o,!1))}},[a,d]),(0,o.N)(()=>()=>{y.current&&cancelAnimationFrame(y.current)},[y]),function(e,t,l){let n=(0,i.useRef)("undefined"!=typeof document?(0,r.T)(e.current?e.current[0]:void 0).activeElement:null);(0,o.N)(()=>{let n=e.current,o=(0,r.T)(n?n[0]:void 0);if(!t||l)return;let i=()=>{(!c||K(c,e))&&v(o.activeElement,e.current)&&(c=e)};return o.addEventListener("focusin",i,!1),null==n||n.forEach(e=>e.addEventListener("focusin",i,!1)),()=>{o.removeEventListener("focusin",i,!1),null==n||n.forEach(e=>e.removeEventListener("focusin",i,!1))}},[e,l]),(0,o.N)(()=>{let n=(0,r.T)(e.current?e.current[0]:void 0);if(!t)return;let o=t=>{if("Tab"!==t.key||t.altKey||t.ctrlKey||t.metaKey||!h(e)||t.isComposing)return;let l=n.activeElement;if(!g(l,e)||!T(e))return;let r=x.getTreeNode(e);if(!r)return;let o=r.nodeToRestore,i=N(n.body,{tabbable:!0});i.currentNode=l;let s=t.shiftKey?i.previousNode():i.nextNode();if(o&&n.body.contains(o)&&o!==n.body||(o=void 0,r.nodeToRestore=void 0),(!s||!g(s,e))&&o){i.currentNode=o;do s=t.shiftKey?i.previousNode():i.nextNode();while(g(s,e));(t.preventDefault(),t.stopPropagation(),s)?b(s,!0):g(o)?b(o,!0):l.blur()}};return l||n.addEventListener("keydown",o,!0),()=>{l||n.removeEventListener("keydown",o,!0)}},[e,t,l]),(0,o.N)(()=>{var l;let o=(0,r.T)(e.current?e.current[0]:void 0);if(!t)return;let i=x.getTreeNode(e);if(i)return i.nodeToRestore=null!=(l=n.current)?l:void 0,()=>{let l=x.getTreeNode(e);if(!l)return;let n=l.nodeToRestore;if(t&&n&&(o.activeElement&&g(o.activeElement,e)||o.activeElement===o.body&&T(e))){let t=x.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let l=t.getTreeNode(e);for(;l;){if(l.nodeToRestore&&l.nodeToRestore.isConnected)return void w(l.nodeToRestore);l=l.parent}for(l=t.getTreeNode(e);l;){if(l.scopeRef&&l.scopeRef.current&&x.getTreeNode(l.scopeRef))return void w(S(l.scopeRef.current,!0));l=l.parent}}})}}},[e,t])}(L,F,k),function(e,t){let l=i.useRef(t);(0,i.useEffect)(()=>{l.current&&(c=e,!v((0,r.T)(e.current?e.current[0]:void 0).activeElement,c.current)&&e.current&&E(e.current)),l.current=!1},[e])}(L,M),(0,i.useEffect)(()=>{let e=(0,r.T)(L.current?L.current[0]:void 0).activeElement,t=null;if(v(e,L.current)){for(let l of x.traverse())l.scopeRef&&v(e,l.scopeRef.current)&&(t=l);t===x.getTreeNode(L)&&(c=t.scopeRef)}},[L]),(0,o.N)(()=>()=>{var e,t,l;let n=null!=(l=null==(t=x.getTreeNode(L))||null==(e=t.parent)?void 0:e.scopeRef)?l:null;(L===c||K(L,c))&&(!n||x.getTreeNode(n))&&(c=n),x.removeTreeNode(L)},[L]);let A=(0,i.useMemo)(()=>{var e;return e=L,{focusNext(t={}){let l=e.current,{from:n,tabbable:o,wrap:i,accept:s}=t,u=n||(0,r.T)(l[0]).activeElement,c=l[0].previousElementSibling,a=N(p(l),{tabbable:o,accept:s},l);a.currentNode=v(u,l)?u:c;let d=a.nextNode();return!d&&i&&(a.currentNode=c,d=a.nextNode()),d&&b(d,!0),d},focusPrevious(t={}){let l=e.current,{from:n,tabbable:o,wrap:i,accept:s}=t,u=n||(0,r.T)(l[0]).activeElement,c=l[l.length-1].nextElementSibling,a=N(p(l),{tabbable:o,accept:s},l);a.currentNode=v(u,l)?u:c;let d=a.previousNode();return!d&&i&&(a.currentNode=c,d=a.previousNode()),d&&b(d,!0),d},focusFirst(t={}){let l=e.current,{tabbable:n,accept:r}=t,o=N(p(l),{tabbable:n,accept:r},l);o.currentNode=l[0].previousElementSibling;let i=o.nextNode();return i&&b(i,!0),i},focusLast(t={}){let l=e.current,{tabbable:n,accept:r}=t,o=N(p(l),{tabbable:n,accept:r},l);o.currentNode=l[l.length-1].nextElementSibling;let i=o.previousNode();return i&&b(i,!0),i}}},[]),B=(0,i.useMemo)(()=>({focusManager:A,parentNode:P}),[P,A]);return i.createElement(s.Provider,{value:B},i.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:C}),m,i.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:I}))}let d=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[contenteditable]"],f=d.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";d.push('[tabindex]:not([tabindex="-1"]):not([disabled])');let y=d.join(':not([hidden]):not([tabindex="-1"]),');function p(e){return e[0].parentElement}function h(e){let t=x.getTreeNode(c);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function v(e,t){return!!e&&!!t&&t.some(t=>t.contains(e))}function g(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:l}of x.traverse(x.getTreeNode(t)))if(l&&v(e,l.current))return!0;return!1}function m(e){return g(e,c)}function K(e,t){var l;let n=null==(l=x.getTreeNode(t))?void 0:l.parent;for(;n;){if(n.scopeRef===e)return!0;n=n.parent}return!1}function b(e,t=!1){if(null==e||t){if(null!=e)try{e.focus()}catch{}}else try{(0,n.l)(e)}catch{}}function S(e,t=!0){let l=e[0].previousElementSibling,n=p(e),r=N(n,{tabbable:t},e);r.currentNode=l;let o=r.nextNode();return t&&!o&&((r=N(n=p(e),{tabbable:!1},e)).currentNode=l,o=r.nextNode()),o}function E(e,t=!0){b(S(e,t))}function T(e){let t=x.getTreeNode(c);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return(null==t?void 0:t.scopeRef)===e}function w(e){e.dispatchEvent(new CustomEvent(u,{bubbles:!0,cancelable:!0}))&&b(e)}function N(e,t,l){let n=(null==t?void 0:t.tabbable)?y:f,o=(0,r.T)(e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode(e){var o;return(null==t||null==(o=t.from)?void 0:o.contains(e))?NodeFilter.FILTER_REJECT:e.matches(n)&&function e(t,l){return"#comment"!==t.nodeName&&function(e){let t=(0,r.m)(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:l,visibility:n}=e.style,o="none"!==l&&"hidden"!==n&&"collapse"!==n;if(o){let{getComputedStyle:t}=e.ownerDocument.defaultView,{display:l,visibility:n}=t(e);o="none"!==l&&"hidden"!==n&&"collapse"!==n}return o}(t)&&!t.hasAttribute("hidden")&&!t.hasAttribute("data-react-aria-prevent-focus")&&("DETAILS"!==t.nodeName||!l||"SUMMARY"===l.nodeName||t.hasAttribute("open"))&&(!t.parentElement||e(t.parentElement,t))}(e)&&(!l||v(e,l))&&(!(null==t?void 0:t.accept)||t.accept(e))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return(null==t?void 0:t.from)&&(o.currentNode=t.from),o}class k{get size(){return this.fastMap.size}getTreeNode(e){return this.fastMap.get(e)}addTreeNode(e,t,l){let n=this.fastMap.get(null!=t?t:null);if(!n)return;let r=new R({scopeRef:e});n.addChild(r),r.parent=n,this.fastMap.set(e,r),l&&(r.nodeToRestore=l)}addNode(e){this.fastMap.set(e.scopeRef,e)}removeTreeNode(e){if(null===e)return;let t=this.fastMap.get(e);if(!t)return;let l=t.parent;for(let e of this.traverse())e!==t&&t.nodeToRestore&&e.nodeToRestore&&t.scopeRef&&t.scopeRef.current&&v(e.nodeToRestore,t.scopeRef.current)&&(e.nodeToRestore=t.nodeToRestore);let n=t.children;l&&(l.removeChild(t),n.size>0&&n.forEach(e=>l&&l.addChild(e))),this.fastMap.delete(t.scopeRef)}*traverse(e=this.root){if(null!=e.scopeRef&&(yield e),e.children.size>0)for(let t of e.children)yield*this.traverse(t)}clone(){var e,t;let l=new k;for(let n of this.traverse())l.addTreeNode(n.scopeRef,null!=(t=null==(e=n.parent)?void 0:e.scopeRef)?t:null,n.nodeToRestore);return l}constructor(){this.fastMap=new Map,this.root=new R({scopeRef:null}),this.fastMap.set(null,this.root)}}class R{addChild(e){this.children.add(e),e.parent=this}removeChild(e){this.children.delete(e),e.parent=void 0}constructor(e){this.children=new Set,this.contain=!1,this.scopeRef=e.scopeRef}}let x=new k}};