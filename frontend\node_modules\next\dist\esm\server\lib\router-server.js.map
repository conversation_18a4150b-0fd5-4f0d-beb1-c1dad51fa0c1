{"version": 3, "sources": ["../../../src/server/lib/router-server.ts"], "sourcesContent": ["// this must come first as it includes require hooks\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './types'\nimport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Fields } from './router-utils/setup-dev-bundler'\nimport type { NextUrlWithParsedQuery, RequestMeta } from '../request-meta'\n\n// This is required before other imports to ensure the require hook is setup.\nimport '../node-environment'\nimport '../require-hook'\n\nimport url from 'url'\nimport path from 'path'\nimport loadConfig from '../config'\nimport { serveStatic } from '../serve-static'\nimport setupDebug from 'next/dist/compiled/debug'\nimport * as Log from '../../build/output/log'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport { setupFsCheck } from './router-utils/filesystem'\nimport { proxyRequest } from './router-utils/proxy-request'\nimport { isAbortError, pipeToNodeResponse } from '../pipe-readable'\nimport { getResolveRoutes } from './router-utils/resolve-routes'\nimport { addRequestMeta, getRequestMeta } from '../request-meta'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport setupCompression from 'next/dist/compiled/compression'\nimport { NoFallbackError } from '../base-server'\nimport { signalFromNodeResponse } from '../web/spec-extension/adapters/next-request'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { parseUrl as parseUrlUtil } from '../../shared/lib/router/utils/parse-url'\n\nimport {\n  PHASE_PRODUCTION_SERVER,\n  PHASE_DEVELOPMENT_SERVER,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../../shared/lib/constants'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { DevBundlerService } from './dev-bundler-service'\nimport { type Span, trace } from '../../trace'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { MockedResponse } from './mock-request'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type AppIsrManifestAction,\n} from '../dev/hot-reloader-types'\nimport { normalizedAssetPrefix } from '../../shared/lib/normalized-asset-prefix'\nimport { NEXT_PATCH_SYMBOL } from './patch-fetch'\nimport type { ServerInitResult } from './render-server'\nimport { filterInternalHeaders } from './server-ipc/utils'\nimport { blockCrossSite } from './router-utils/block-cross-site'\nimport { traceGlobals } from '../../trace/shared'\n\nconst debug = setupDebug('next:router-server:main')\nconst isNextFont = (pathname: string | null) =>\n  pathname && /\\/media\\/[^/]+\\.(woff|woff2|eot|ttf|otf)$/.test(pathname)\n\nexport type RenderServer = Pick<\n  typeof import('./render-server'),\n  | 'initialize'\n  | 'clearModuleContext'\n  | 'propagateServerField'\n  | 'getServerField'\n>\n\nexport interface LazyRenderServerInstance {\n  instance?: RenderServer\n}\n\nconst requestHandlers: Record<string, WorkerRequestHandler> = {}\n\nexport async function initialize(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  customServer?: boolean\n  experimentalHttpsServer?: boolean\n  startServerSpan?: Span\n  quiet?: boolean\n}): Promise<ServerInitResult> {\n  if (!process.env.NODE_ENV) {\n    // @ts-ignore not readonly\n    process.env.NODE_ENV = opts.dev ? 'development' : 'production'\n  }\n\n  const config = await loadConfig(\n    opts.dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_SERVER,\n    opts.dir,\n    { silent: false }\n  )\n\n  let compress: ReturnType<typeof setupCompression> | undefined\n\n  if (config?.compress !== false) {\n    compress = setupCompression()\n  }\n\n  const fsChecker = await setupFsCheck({\n    dev: opts.dev,\n    dir: opts.dir,\n    config,\n    minimalMode: opts.minimalMode,\n  })\n\n  const renderServer: LazyRenderServerInstance = {}\n\n  let developmentBundler: DevBundler | undefined\n\n  let devBundlerService: DevBundlerService | undefined\n\n  let originalFetch = globalThis.fetch\n\n  if (opts.dev) {\n    const { Telemetry } =\n      require('../../telemetry/storage') as typeof import('../../telemetry/storage')\n\n    const telemetry = new Telemetry({\n      distDir: path.join(opts.dir, config.distDir),\n    })\n    traceGlobals.set('telemetry', telemetry)\n\n    const { pagesDir, appDir } = findPagesDir(opts.dir)\n\n    const { setupDevBundler } =\n      require('./router-utils/setup-dev-bundler') as typeof import('./router-utils/setup-dev-bundler')\n\n    const resetFetch = () => {\n      globalThis.fetch = originalFetch\n      ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = false\n    }\n\n    const setupDevBundlerSpan = opts.startServerSpan\n      ? opts.startServerSpan.traceChild('setup-dev-bundler')\n      : trace('setup-dev-bundler')\n    developmentBundler = await setupDevBundlerSpan.traceAsyncFn(() =>\n      setupDevBundler({\n        // Passed here but the initialization of this object happens below, doing the initialization before the setupDev call breaks.\n        renderServer,\n        appDir,\n        pagesDir,\n        telemetry,\n        fsChecker,\n        dir: opts.dir,\n        nextConfig: config,\n        isCustomServer: opts.customServer,\n        turbo: !!process.env.TURBOPACK,\n        port: opts.port,\n        onDevServerCleanup: opts.onDevServerCleanup,\n        resetFetch,\n      })\n    )\n\n    devBundlerService = new DevBundlerService(\n      developmentBundler,\n      // The request handler is assigned below, this allows us to create a lazy\n      // reference to it.\n      (req, res) => {\n        return requestHandlers[opts.dir](req, res)\n      }\n    )\n  }\n\n  renderServer.instance =\n    require('./render-server') as typeof import('./render-server')\n\n  const requestHandlerImpl: WorkerRequestHandler = async (req, res) => {\n    // internal headers should not be honored by the request handler\n    if (!process.env.NEXT_PRIVATE_TEST_HEADERS) {\n      filterInternalHeaders(req.headers)\n    }\n\n    if (\n      !opts.minimalMode &&\n      config.i18n &&\n      config.i18n.localeDetection !== false\n    ) {\n      const urlParts = (req.url || '').split('?', 1)\n      let urlNoQuery = urlParts[0] || ''\n\n      if (config.basePath) {\n        urlNoQuery = removePathPrefix(urlNoQuery, config.basePath)\n      }\n\n      const pathnameInfo = getNextPathnameInfo(urlNoQuery, {\n        nextConfig: config,\n      })\n\n      const domainLocale = detectDomainLocale(\n        config.i18n.domains,\n        getHostname({ hostname: urlNoQuery }, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || config.i18n.defaultLocale\n\n      const { getLocaleRedirect } =\n        require('../../shared/lib/i18n/get-locale-redirect') as typeof import('../../shared/lib/i18n/get-locale-redirect')\n\n      const parsedUrl = parseUrlUtil((req.url || '')?.replace(/^\\/+/, '/'))\n\n      const redirect = getLocaleRedirect({\n        defaultLocale,\n        domainLocale,\n        headers: req.headers,\n        nextConfig: config,\n        pathLocale: pathnameInfo.locale,\n        urlParsed: {\n          ...parsedUrl,\n          pathname: pathnameInfo.locale\n            ? `/${pathnameInfo.locale}${urlNoQuery}`\n            : urlNoQuery,\n        },\n      })\n\n      if (redirect) {\n        res.setHeader('Location', redirect)\n        res.statusCode = RedirectStatusCode.TemporaryRedirect\n        res.end(redirect)\n        return\n      }\n    }\n\n    if (compress) {\n      // @ts-expect-error not express req/res\n      compress(req, res, () => {})\n    }\n    req.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n    res.on('error', (_err) => {\n      // TODO: log socket errors?\n    })\n\n    const invokedOutputs = new Set<string>()\n\n    async function invokeRender(\n      parsedUrl: NextUrlWithParsedQuery,\n      invokePath: string,\n      handleIndex: number,\n      additionalRequestMeta?: RequestMeta\n    ) {\n      // invokeRender expects /api routes to not be locale prefixed\n      // so normalize here before continuing\n      if (\n        config.i18n &&\n        removePathPrefix(invokePath, config.basePath).startsWith(\n          `/${getRequestMeta(req, 'locale')}/api`\n        )\n      ) {\n        invokePath = fsChecker.handleLocale(\n          removePathPrefix(invokePath, config.basePath)\n        ).pathname\n      }\n\n      if (\n        req.headers['x-nextjs-data'] &&\n        fsChecker.getMiddlewareMatchers()?.length &&\n        removePathPrefix(invokePath, config.basePath) === '/404'\n      ) {\n        res.setHeader('x-nextjs-matched-path', parsedUrl.pathname || '')\n        res.statusCode = 404\n        res.setHeader('content-type', 'application/json')\n        res.end('{}')\n        return null\n      }\n\n      if (!handlers) {\n        throw new Error('Failed to initialize render server')\n      }\n\n      addRequestMeta(req, 'invokePath', invokePath)\n      addRequestMeta(req, 'invokeQuery', parsedUrl.query)\n      addRequestMeta(req, 'middlewareInvoke', false)\n\n      for (const key in additionalRequestMeta || {}) {\n        addRequestMeta(\n          req,\n          key as keyof RequestMeta,\n          additionalRequestMeta![key as keyof RequestMeta]\n        )\n      }\n\n      debug('invokeRender', req.url, req.headers)\n\n      try {\n        const initResult =\n          await renderServer?.instance?.initialize(renderServerOpts)\n        try {\n          await initResult?.requestHandler(req, res)\n        } catch (err) {\n          if (err instanceof NoFallbackError) {\n            // eslint-disable-next-line\n            await handleRequest(handleIndex + 1)\n            return\n          }\n          throw err\n        }\n        return\n      } catch (e) {\n        // If the client aborts before we can receive a response object (when\n        // the headers are flushed), then we can early exit without further\n        // processing.\n        if (isAbortError(e)) {\n          return\n        }\n        throw e\n      }\n    }\n\n    const handleRequest = async (handleIndex: number) => {\n      if (handleIndex > 5) {\n        throw new Error(`Attempted to handle request too many times ${req.url}`)\n      }\n\n      // handle hot-reloader first\n      if (developmentBundler) {\n        if (blockCrossSite(req, res, config.allowedDevOrigins, opts.hostname)) {\n          return\n        }\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        }\n        const parsedUrl = url.parse(req.url || '/')\n\n        const hotReloaderResult = await developmentBundler.hotReloader.run(\n          req,\n          res,\n          parsedUrl\n        )\n\n        if (hotReloaderResult.finished) {\n          return hotReloaderResult\n        }\n        req.url = origUrl\n      }\n\n      const {\n        finished,\n        parsedUrl,\n        statusCode,\n        resHeaders,\n        bodyStream,\n        matchedOutput,\n      } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: false,\n        signal: signalFromNodeResponse(res),\n        invokedOutputs,\n      })\n\n      if (res.closed || res.finished) {\n        return\n      }\n\n      if (developmentBundler && matchedOutput?.type === 'devVirtualFsItem') {\n        const origUrl = req.url || '/'\n\n        if (config.basePath && pathHasPrefix(origUrl, config.basePath)) {\n          req.url = removePathPrefix(origUrl, config.basePath)\n        }\n\n        if (resHeaders) {\n          for (const key of Object.keys(resHeaders)) {\n            res.setHeader(key, resHeaders[key])\n          }\n        }\n        const result = await developmentBundler.requestHandler(req, res)\n\n        if (result.finished) {\n          return\n        }\n        // TODO: throw invariant if we resolved to this but it wasn't handled?\n        req.url = origUrl\n      }\n\n      debug('requestHandler!', req.url, {\n        matchedOutput,\n        statusCode,\n        resHeaders,\n        bodyStream: !!bodyStream,\n        parsedUrl: {\n          pathname: parsedUrl.pathname,\n          query: parsedUrl.query,\n        },\n        finished,\n      })\n\n      // apply any response headers from routing\n      for (const key of Object.keys(resHeaders || {})) {\n        res.setHeader(key, resHeaders[key])\n      }\n\n      // handle redirect\n      if (!bodyStream && statusCode && statusCode > 300 && statusCode < 400) {\n        const destination = url.format(parsedUrl)\n        res.statusCode = statusCode\n        res.setHeader('location', destination)\n\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n          res.setHeader('Refresh', `0;url=${destination}`)\n        }\n        return res.end(destination)\n      }\n\n      // handle middleware body response\n      if (bodyStream) {\n        res.statusCode = statusCode || 200\n        return await pipeToNodeResponse(bodyStream, res)\n      }\n\n      if (finished && parsedUrl.protocol) {\n        return await proxyRequest(\n          req,\n          res,\n          parsedUrl,\n          undefined,\n          getRequestMeta(req, 'clonableBody')?.cloneBodyStream(),\n          config.experimental.proxyTimeout\n        )\n      }\n\n      if (matchedOutput?.fsPath && matchedOutput.itemPath) {\n        if (\n          opts.dev &&\n          (fsChecker.appFiles.has(matchedOutput.itemPath) ||\n            fsChecker.pageFiles.has(matchedOutput.itemPath))\n        ) {\n          res.statusCode = 500\n          const message = `A conflicting public file and page file was found for path ${matchedOutput.itemPath} https://nextjs.org/docs/messages/conflicting-public-file-page`\n          await invokeRender(parsedUrl, '/_error', handleIndex, {\n            invokeStatus: 500,\n            invokeError: new Error(message),\n          })\n          Log.error(message)\n          return\n        }\n\n        if (\n          !res.getHeader('cache-control') &&\n          matchedOutput.type === 'nextStaticFolder'\n        ) {\n          if (opts.dev && !isNextFont(parsedUrl.pathname)) {\n            res.setHeader('Cache-Control', 'no-store, must-revalidate')\n          } else {\n            res.setHeader(\n              'Cache-Control',\n              'public, max-age=31536000, immutable'\n            )\n          }\n        }\n        if (!(req.method === 'GET' || req.method === 'HEAD')) {\n          res.setHeader('Allow', ['GET', 'HEAD'])\n          res.statusCode = 405\n          return await invokeRender(\n            url.parse('/405', true),\n            '/405',\n            handleIndex,\n            {\n              invokeStatus: 405,\n            }\n          )\n        }\n\n        try {\n          return await serveStatic(req, res, matchedOutput.itemPath, {\n            root: matchedOutput.itemsRoot,\n            // Ensures that etags are not generated for static files when disabled.\n            etag: config.generateEtags,\n          })\n        } catch (err: any) {\n          /**\n           * Hardcoded every possible error status code that could be thrown by \"serveStatic\" method\n           * This is done by searching \"this.error\" inside \"send\" module's source code:\n           * https://github.com/pillarjs/send/blob/master/index.js\n           * https://github.com/pillarjs/send/blob/develop/index.js\n           */\n          const POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC = new Set([\n            // send module will throw 500 when header is already sent or fs.stat error happens\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L392\n            // Note: we will use Next.js built-in 500 page to handle 500 errors\n            // 500,\n\n            // send module will throw 404 when file is missing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L421\n            // Note: we will use Next.js built-in 404 page to handle 404 errors\n            // 404,\n\n            // send module will throw 403 when redirecting to a directory without enabling directory listing\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L484\n            // Note: Next.js throws a different error (without status code) for directory listing\n            // 403,\n\n            // send module will throw 400 when fails to normalize the path\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L520\n            400,\n\n            // send module will throw 412 with conditional GET request\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L632\n            412,\n\n            // send module will throw 416 when range is not satisfiable\n            // https://github.com/pillarjs/send/blob/53f0ab476145670a9bdd3dc722ab2fdc8d358fc6/index.js#L669\n            416,\n          ])\n\n          let validErrorStatus = POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC.has(\n            err.statusCode\n          )\n\n          // normalize non-allowed status codes\n          if (!validErrorStatus) {\n            ;(err as any).statusCode = 400\n          }\n\n          if (typeof err.statusCode === 'number') {\n            const invokePath = `/${err.statusCode}`\n            const invokeStatus = err.statusCode\n            res.statusCode = err.statusCode\n            return await invokeRender(\n              url.parse(invokePath, true),\n              invokePath,\n              handleIndex,\n              {\n                invokeStatus,\n              }\n            )\n          }\n          throw err\n        }\n      }\n\n      if (matchedOutput) {\n        invokedOutputs.add(matchedOutput.itemPath)\n\n        return await invokeRender(\n          parsedUrl,\n          parsedUrl.pathname || '/',\n          handleIndex,\n          {\n            invokeOutput: matchedOutput.itemPath,\n          }\n        )\n      }\n\n      // 404 case\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n\n      // Short-circuit favicon.ico serving so that the 404 page doesn't get built as favicon is requested by the browser when loading any route.\n      if (opts.dev && !matchedOutput && parsedUrl.pathname === '/favicon.ico') {\n        res.statusCode = 404\n        res.end('')\n        return null\n      }\n\n      const appNotFound = opts.dev\n        ? developmentBundler?.serverFields.hasAppNotFound\n        : await fsChecker.getItem(UNDERSCORE_NOT_FOUND_ROUTE)\n\n      res.statusCode = 404\n\n      if (appNotFound) {\n        return await invokeRender(\n          parsedUrl,\n          UNDERSCORE_NOT_FOUND_ROUTE,\n          handleIndex,\n          {\n            invokeStatus: 404,\n          }\n        )\n      }\n\n      await invokeRender(parsedUrl, '/404', handleIndex, {\n        invokeStatus: 404,\n      })\n    }\n\n    try {\n      await handleRequest(0)\n    } catch (err) {\n      try {\n        let invokePath = '/500'\n        let invokeStatus = '500'\n\n        if (err instanceof DecodeError) {\n          invokePath = '/400'\n          invokeStatus = '400'\n        } else {\n          console.error(err)\n        }\n        res.statusCode = Number(invokeStatus)\n        return await invokeRender(url.parse(invokePath, true), invokePath, 0, {\n          invokeStatus: res.statusCode,\n        })\n      } catch (err2) {\n        console.error(err2)\n      }\n      res.statusCode = 500\n      res.end('Internal Server Error')\n    }\n  }\n\n  let requestHandler: WorkerRequestHandler = requestHandlerImpl\n  if (config.experimental.testProxy) {\n    // Intercept fetch and other testmode apis.\n    const { wrapRequestHandlerWorker, interceptTestApis } =\n      require('next/dist/experimental/testmode/server') as typeof import('next/src/experimental/testmode/server')\n    requestHandler = wrapRequestHandlerWorker(requestHandler)\n    interceptTestApis()\n    // We treat the intercepted fetch as \"original\" fetch that should be reset to during HMR.\n    originalFetch = globalThis.fetch\n  }\n  requestHandlers[opts.dir] = requestHandler\n\n  const renderServerOpts: Parameters<RenderServer['initialize']>[0] = {\n    port: opts.port,\n    dir: opts.dir,\n    hostname: opts.hostname,\n    minimalMode: opts.minimalMode,\n    dev: !!opts.dev,\n    server: opts.server,\n    serverFields: {\n      ...(developmentBundler?.serverFields || {}),\n      setIsrStatus: devBundlerService?.setIsrStatus.bind(devBundlerService),\n    } satisfies ServerFields,\n    experimentalTestProxy: !!config.experimental.testProxy,\n    experimentalHttpsServer: !!opts.experimentalHttpsServer,\n    bundlerService: devBundlerService,\n    startServerSpan: opts.startServerSpan,\n    quiet: opts.quiet,\n    onDevServerCleanup: opts.onDevServerCleanup,\n  }\n  renderServerOpts.serverFields.routerServerHandler = requestHandlerImpl\n\n  // pre-initialize workers\n  const handlers = await renderServer.instance.initialize(renderServerOpts)\n\n  const logError = async (\n    type: 'uncaughtException' | 'unhandledRejection',\n    err: Error | undefined\n  ) => {\n    if (isPostpone(err)) {\n      // React postpones that are unhandled might end up logged here but they're\n      // not really errors. They're just part of rendering.\n      return\n    }\n    if (type === 'unhandledRejection') {\n      Log.error('unhandledRejection: ', err)\n    } else if (type === 'uncaughtException') {\n      Log.error('uncaughtException: ', err)\n    }\n  }\n\n  process.on('uncaughtException', logError.bind(null, 'uncaughtException'))\n  process.on('unhandledRejection', logError.bind(null, 'unhandledRejection'))\n\n  const resolveRoutes = getResolveRoutes(\n    fsChecker,\n    config,\n    opts,\n    renderServer.instance,\n    renderServerOpts,\n    developmentBundler?.ensureMiddleware\n  )\n\n  const upgradeHandler: WorkerUpgradeHandler = async (req, socket, head) => {\n    try {\n      req.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n      socket.on('error', (_err) => {\n        // TODO: log socket errors?\n        // console.error(_err);\n      })\n\n      if (opts.dev && developmentBundler && req.url) {\n        if (\n          blockCrossSite(req, socket, config.allowedDevOrigins, opts.hostname)\n        ) {\n          return\n        }\n        const { basePath, assetPrefix } = config\n\n        let hmrPrefix = basePath\n\n        // assetPrefix overrides basePath for HMR path\n        if (assetPrefix) {\n          hmrPrefix = normalizedAssetPrefix(assetPrefix)\n\n          if (URL.canParse(hmrPrefix)) {\n            // remove trailing slash from pathname\n            // return empty string if pathname is '/'\n            // to avoid conflicts with '/_next' below\n            hmrPrefix = new URL(hmrPrefix).pathname.replace(/\\/$/, '')\n          }\n        }\n\n        const isHMRRequest = req.url.startsWith(\n          ensureLeadingSlash(`${hmrPrefix}/_next/webpack-hmr`)\n        )\n\n        // only handle HMR requests if the basePath in the request\n        // matches the basePath for the handler responding to the request\n        if (isHMRRequest) {\n          return developmentBundler.hotReloader.onHMR(\n            req,\n            socket,\n            head,\n            (client) => {\n              client.send(\n                JSON.stringify({\n                  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST,\n                  data: devBundlerService?.appIsrManifest || {},\n                } satisfies AppIsrManifestAction)\n              )\n            }\n          )\n        }\n      }\n\n      const res = new MockedResponse({\n        resWriter: () => {\n          throw new Error(\n            'Invariant: did not expect response writer to be written to for upgrade request'\n          )\n        },\n      })\n      const { matchedOutput, parsedUrl } = await resolveRoutes({\n        req,\n        res,\n        isUpgradeReq: true,\n        signal: signalFromNodeResponse(socket),\n      })\n\n      // TODO: allow upgrade requests to pages/app paths?\n      // this was not previously supported\n      if (matchedOutput) {\n        return socket.end()\n      }\n\n      if (parsedUrl.protocol) {\n        return await proxyRequest(req, socket, parsedUrl, head)\n      }\n\n      // If there's no matched output, we don't handle the request as user's\n      // custom WS server may be listening on the same path.\n    } catch (err) {\n      console.error('Error handling upgrade request', err)\n      socket.end()\n    }\n  }\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server: handlers.server,\n    closeUpgraded() {\n      developmentBundler?.hotReloader?.close()\n    },\n  }\n}\n"], "names": ["url", "path", "loadConfig", "serveStatic", "setupDebug", "Log", "DecodeError", "findPagesDir", "setupFsCheck", "proxyRequest", "isAbortError", "pipeToNodeResponse", "getResolveRoutes", "addRequestMeta", "getRequestMeta", "pathHasPrefix", "removePathPrefix", "setupCompression", "NoFallbackError", "signalFromNodeResponse", "isPostpone", "parseUrl", "parseUrlUtil", "PHASE_PRODUCTION_SERVER", "PHASE_DEVELOPMENT_SERVER", "UNDERSCORE_NOT_FOUND_ROUTE", "RedirectStatusCode", "DevBundlerService", "trace", "ensureLeadingSlash", "getNextPathnameInfo", "getHostname", "detectDomainLocale", "MockedResponse", "HMR_ACTIONS_SENT_TO_BROWSER", "normalizedAssetPrefix", "NEXT_PATCH_SYMBOL", "filterInternalHeaders", "blockCrossSite", "traceGlobals", "debug", "isNextFont", "pathname", "test", "requestHandlers", "initialize", "opts", "process", "env", "NODE_ENV", "dev", "config", "dir", "silent", "compress", "fs<PERSON><PERSON><PERSON>", "minimalMode", "renderServer", "developmentBundler", "devBundlerService", "originalFetch", "globalThis", "fetch", "Telemetry", "require", "telemetry", "distDir", "join", "set", "pagesDir", "appDir", "setupDevBundler", "resetFetch", "setupDevBundlerSpan", "startServerSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "nextConfig", "isCustomServer", "customServer", "turbo", "TURBOPACK", "port", "onDevServerCleanup", "req", "res", "instance", "requestHandlerImpl", "NEXT_PRIVATE_TEST_HEADERS", "headers", "i18n", "localeDetection", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basePath", "pathnameInfo", "domainLocale", "domains", "hostname", "defaultLocale", "getLocaleRedirect", "parsedUrl", "replace", "redirect", "pathLocale", "locale", "urlParsed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "TemporaryRedirect", "end", "on", "_err", "invokedOutputs", "Set", "invokeRender", "invoke<PERSON><PERSON>", "handleIndex", "additionalRequestMeta", "startsWith", "handleLocale", "getMiddlewareMatchers", "length", "handlers", "Error", "query", "key", "initResult", "renderServerOpts", "requestHandler", "err", "handleRequest", "e", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "origUrl", "parse", "hotReloaderResult", "hotReloader", "run", "finished", "resHeaders", "bodyStream", "matchedOutput", "resolveRoutes", "isUpgradeReq", "signal", "closed", "type", "Object", "keys", "result", "destination", "format", "PermanentRedirect", "protocol", "undefined", "cloneBodyStream", "experimental", "proxyTimeout", "fsPath", "itemPath", "appFiles", "has", "pageFiles", "message", "invoke<PERSON>tatus", "invokeError", "error", "<PERSON><PERSON><PERSON><PERSON>", "method", "root", "itemsRoot", "etag", "generateEtags", "POSSIBLE_ERROR_CODE_FROM_SERVE_STATIC", "validErrorStatus", "add", "invokeOutput", "appNotFound", "serverFields", "hasAppNotFound", "getItem", "console", "Number", "err2", "testProxy", "wrapRequestHandlerWorker", "interceptTestApis", "server", "setIsrStatus", "bind", "experimentalTestProxy", "experimentalHttpsServer", "bundlerService", "quiet", "routerServerHandler", "logError", "ensureMiddleware", "upgradeHandler", "socket", "head", "assetPrefix", "hmrPrefix", "URL", "canParse", "isHMRRequest", "onHMR", "client", "send", "JSON", "stringify", "action", "ISR_MANIFEST", "data", "appIsrManifest", "resWriter", "closeUpgraded", "close"], "mappings": "AAAA,oDAAoD;AAKpD,6EAA6E;AAC7E,OAAO,sBAAqB;AAC5B,OAAO,kBAAiB;AAExB,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,OAAOC,gBAAgB,YAAW;AAClC,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,mBAAkB;AACnE,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,cAAc,EAAEC,cAAc,QAAQ,kBAAiB;AAChE,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,sBAAsB,iCAAgC;AAC7D,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,UAAU,QAAQ,6BAA4B;AACvD,SAASC,YAAYC,YAAY,QAAQ,0CAAyC;AAElF,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,0BAA0B,QACrB,6BAA4B;AACnC,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,kBAAkB,QAAQ,kDAAiD;AACpF,SAASC,mBAAmB,QAAQ,uDAAsD;AAC1F,SAASC,WAAW,QAAQ,gCAA+B;AAC3D,SAASC,kBAAkB,QAAQ,6CAA4C;AAC/E,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SACEC,2BAA2B,QAEtB,4BAA2B;AAClC,SAASC,qBAAqB,QAAQ,2CAA0C;AAChF,SAASC,iBAAiB,QAAQ,gBAAe;AAEjD,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,cAAc,QAAQ,kCAAiC;AAChE,SAASC,YAAY,QAAQ,qBAAoB;AAEjD,MAAMC,QAAQpC,WAAW;AACzB,MAAMqC,aAAa,CAACC,WAClBA,YAAY,4CAA4CC,IAAI,CAACD;AAc/D,MAAME,kBAAwD,CAAC;AAE/D,OAAO,eAAeC,WAAWC,IAahC;IACC,IAAI,CAACC,QAAQC,GAAG,CAACC,QAAQ,EAAE;QACzB,0BAA0B;QAC1BF,QAAQC,GAAG,CAACC,QAAQ,GAAGH,KAAKI,GAAG,GAAG,gBAAgB;IACpD;IAEA,MAAMC,SAAS,MAAMjD,WACnB4C,KAAKI,GAAG,GAAG1B,2BAA2BD,yBACtCuB,KAAKM,GAAG,EACR;QAAEC,QAAQ;IAAM;IAGlB,IAAIC;IAEJ,IAAIH,CAAAA,0BAAAA,OAAQG,QAAQ,MAAK,OAAO;QAC9BA,WAAWrC;IACb;IAEA,MAAMsC,YAAY,MAAM/C,aAAa;QACnC0C,KAAKJ,KAAKI,GAAG;QACbE,KAAKN,KAAKM,GAAG;QACbD;QACAK,aAAaV,KAAKU,WAAW;IAC/B;IAEA,MAAMC,eAAyC,CAAC;IAEhD,IAAIC;IAEJ,IAAIC;IAEJ,IAAIC,gBAAgBC,WAAWC,KAAK;IAEpC,IAAIhB,KAAKI,GAAG,EAAE;QACZ,MAAM,EAAEa,SAAS,EAAE,GACjBC,QAAQ;QAEV,MAAMC,YAAY,IAAIF,UAAU;YAC9BG,SAASjE,KAAKkE,IAAI,CAACrB,KAAKM,GAAG,EAAED,OAAOe,OAAO;QAC7C;QACA3B,aAAa6B,GAAG,CAAC,aAAaH;QAE9B,MAAM,EAAEI,QAAQ,EAAEC,MAAM,EAAE,GAAG/D,aAAauC,KAAKM,GAAG;QAElD,MAAM,EAAEmB,eAAe,EAAE,GACvBP,QAAQ;QAEV,MAAMQ,aAAa;YACjBX,WAAWC,KAAK,GAAGF;YACjBC,UAAsC,CAACzB,kBAAkB,GAAG;QAChE;QAEA,MAAMqC,sBAAsB3B,KAAK4B,eAAe,GAC5C5B,KAAK4B,eAAe,CAACC,UAAU,CAAC,uBAChC/C,MAAM;QACV8B,qBAAqB,MAAMe,oBAAoBG,YAAY,CAAC,IAC1DL,gBAAgB;gBACd,6HAA6H;gBAC7Hd;gBACAa;gBACAD;gBACAJ;gBACAV;gBACAH,KAAKN,KAAKM,GAAG;gBACbyB,YAAY1B;gBACZ2B,gBAAgBhC,KAAKiC,YAAY;gBACjCC,OAAO,CAAC,CAACjC,QAAQC,GAAG,CAACiC,SAAS;gBAC9BC,MAAMpC,KAAKoC,IAAI;gBACfC,oBAAoBrC,KAAKqC,kBAAkB;gBAC3CX;YACF;QAGFb,oBAAoB,IAAIhC,kBACtB+B,oBACA,yEAAyE;QACzE,mBAAmB;QACnB,CAAC0B,KAAKC;YACJ,OAAOzC,eAAe,CAACE,KAAKM,GAAG,CAAC,CAACgC,KAAKC;QACxC;IAEJ;IAEA5B,aAAa6B,QAAQ,GACnBtB,QAAQ;IAEV,MAAMuB,qBAA2C,OAAOH,KAAKC;QAC3D,gEAAgE;QAChE,IAAI,CAACtC,QAAQC,GAAG,CAACwC,yBAAyB,EAAE;YAC1CnD,sBAAsB+C,IAAIK,OAAO;QACnC;QAEA,IACE,CAAC3C,KAAKU,WAAW,IACjBL,OAAOuC,IAAI,IACXvC,OAAOuC,IAAI,CAACC,eAAe,KAAK,OAChC;gBAuBgCP;YAtBhC,MAAMQ,WAAW,AAACR,CAAAA,IAAIpF,GAAG,IAAI,EAAC,EAAG6F,KAAK,CAAC,KAAK;YAC5C,IAAIC,aAAaF,QAAQ,CAAC,EAAE,IAAI;YAEhC,IAAIzC,OAAO4C,QAAQ,EAAE;gBACnBD,aAAa9E,iBAAiB8E,YAAY3C,OAAO4C,QAAQ;YAC3D;YAEA,MAAMC,eAAelE,oBAAoBgE,YAAY;gBACnDjB,YAAY1B;YACd;YAEA,MAAM8C,eAAejE,mBACnBmB,OAAOuC,IAAI,CAACQ,OAAO,EACnBnE,YAAY;gBAAEoE,UAAUL;YAAW,GAAGV,IAAIK,OAAO;YAGnD,MAAMW,gBACJH,CAAAA,gCAAAA,aAAcG,aAAa,KAAIjD,OAAOuC,IAAI,CAACU,aAAa;YAE1D,MAAM,EAAEC,iBAAiB,EAAE,GACzBrC,QAAQ;YAEV,MAAMsC,YAAYhF,cAAc8D,QAAAA,IAAIpF,GAAG,IAAI,uBAAZ,AAACoF,MAAgBmB,OAAO,CAAC,QAAQ;YAEhE,MAAMC,WAAWH,kBAAkB;gBACjCD;gBACAH;gBACAR,SAASL,IAAIK,OAAO;gBACpBZ,YAAY1B;gBACZsD,YAAYT,aAAaU,MAAM;gBAC/BC,WAAW;oBACT,GAAGL,SAAS;oBACZ5D,UAAUsD,aAAaU,MAAM,GACzB,CAAC,CAAC,EAAEV,aAAaU,MAAM,GAAGZ,YAAY,GACtCA;gBACN;YACF;YAEA,IAAIU,UAAU;gBACZnB,IAAIuB,SAAS,CAAC,YAAYJ;gBAC1BnB,IAAIwB,UAAU,GAAGnF,mBAAmBoF,iBAAiB;gBACrDzB,IAAI0B,GAAG,CAACP;gBACR;YACF;QACF;QAEA,IAAIlD,UAAU;YACZ,uCAAuC;YACvCA,SAAS8B,KAAKC,KAAK,KAAO;QAC5B;QACAD,IAAI4B,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QACA5B,IAAI2B,EAAE,CAAC,SAAS,CAACC;QACf,2BAA2B;QAC7B;QAEA,MAAMC,iBAAiB,IAAIC;QAE3B,eAAeC,aACbd,SAAiC,EACjCe,UAAkB,EAClBC,WAAmB,EACnBC,qBAAmC;gBAiBjChE;YAfF,6DAA6D;YAC7D,sCAAsC;YACtC,IACEJ,OAAOuC,IAAI,IACX1E,iBAAiBqG,YAAYlE,OAAO4C,QAAQ,EAAEyB,UAAU,CACtD,CAAC,CAAC,EAAE1G,eAAesE,KAAK,UAAU,IAAI,CAAC,GAEzC;gBACAiC,aAAa9D,UAAUkE,YAAY,CACjCzG,iBAAiBqG,YAAYlE,OAAO4C,QAAQ,GAC5CrD,QAAQ;YACZ;YAEA,IACE0C,IAAIK,OAAO,CAAC,gBAAgB,MAC5BlC,mCAAAA,UAAUmE,qBAAqB,uBAA/BnE,iCAAmCoE,MAAM,KACzC3G,iBAAiBqG,YAAYlE,OAAO4C,QAAQ,MAAM,QAClD;gBACAV,IAAIuB,SAAS,CAAC,yBAAyBN,UAAU5D,QAAQ,IAAI;gBAC7D2C,IAAIwB,UAAU,GAAG;gBACjBxB,IAAIuB,SAAS,CAAC,gBAAgB;gBAC9BvB,IAAI0B,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,IAAI,CAACa,UAAU;gBACb,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEAhH,eAAeuE,KAAK,cAAciC;YAClCxG,eAAeuE,KAAK,eAAekB,UAAUwB,KAAK;YAClDjH,eAAeuE,KAAK,oBAAoB;YAExC,IAAK,MAAM2C,OAAOR,yBAAyB,CAAC,EAAG;gBAC7C1G,eACEuE,KACA2C,KACAR,qBAAsB,CAACQ,IAAyB;YAEpD;YAEAvF,MAAM,gBAAgB4C,IAAIpF,GAAG,EAAEoF,IAAIK,OAAO;YAE1C,IAAI;oBAEMhC;gBADR,MAAMuE,aACJ,OAAMvE,iCAAAA,yBAAAA,aAAc6B,QAAQ,qBAAtB7B,uBAAwBZ,UAAU,CAACoF;gBAC3C,IAAI;oBACF,OAAMD,8BAAAA,WAAYE,cAAc,CAAC9C,KAAKC;gBACxC,EAAE,OAAO8C,KAAK;oBACZ,IAAIA,eAAejH,iBAAiB;wBAClC,2BAA2B;wBAC3B,MAAMkH,cAAcd,cAAc;wBAClC;oBACF;oBACA,MAAMa;gBACR;gBACA;YACF,EAAE,OAAOE,GAAG;gBACV,qEAAqE;gBACrE,mEAAmE;gBACnE,cAAc;gBACd,IAAI3H,aAAa2H,IAAI;oBACnB;gBACF;gBACA,MAAMA;YACR;QACF;QAEA,MAAMD,gBAAgB,OAAOd;YAC3B,IAAIA,cAAc,GAAG;gBACnB,MAAM,qBAAkE,CAAlE,IAAIO,MAAM,CAAC,2CAA2C,EAAEzC,IAAIpF,GAAG,EAAE,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;YAEA,4BAA4B;YAC5B,IAAI0D,oBAAoB;gBACtB,IAAIpB,eAAe8C,KAAKC,KAAKlC,OAAOmF,iBAAiB,EAAExF,KAAKqD,QAAQ,GAAG;oBACrE;gBACF;gBACA,MAAMoC,UAAUnD,IAAIpF,GAAG,IAAI;gBAE3B,IAAImD,OAAO4C,QAAQ,IAAIhF,cAAcwH,SAASpF,OAAO4C,QAAQ,GAAG;oBAC9DX,IAAIpF,GAAG,GAAGgB,iBAAiBuH,SAASpF,OAAO4C,QAAQ;gBACrD;gBACA,MAAMO,YAAYtG,IAAIwI,KAAK,CAACpD,IAAIpF,GAAG,IAAI;gBAEvC,MAAMyI,oBAAoB,MAAM/E,mBAAmBgF,WAAW,CAACC,GAAG,CAChEvD,KACAC,KACAiB;gBAGF,IAAImC,kBAAkBG,QAAQ,EAAE;oBAC9B,OAAOH;gBACT;gBACArD,IAAIpF,GAAG,GAAGuI;YACZ;YAEA,MAAM,EACJK,QAAQ,EACRtC,SAAS,EACTO,UAAU,EACVgC,UAAU,EACVC,UAAU,EACVC,aAAa,EACd,GAAG,MAAMC,cAAc;gBACtB5D;gBACAC;gBACA4D,cAAc;gBACdC,QAAQ/H,uBAAuBkE;gBAC/B6B;YACF;YAEA,IAAI7B,IAAI8D,MAAM,IAAI9D,IAAIuD,QAAQ,EAAE;gBAC9B;YACF;YAEA,IAAIlF,sBAAsBqF,CAAAA,iCAAAA,cAAeK,IAAI,MAAK,oBAAoB;gBACpE,MAAMb,UAAUnD,IAAIpF,GAAG,IAAI;gBAE3B,IAAImD,OAAO4C,QAAQ,IAAIhF,cAAcwH,SAASpF,OAAO4C,QAAQ,GAAG;oBAC9DX,IAAIpF,GAAG,GAAGgB,iBAAiBuH,SAASpF,OAAO4C,QAAQ;gBACrD;gBAEA,IAAI8C,YAAY;oBACd,KAAK,MAAMd,OAAOsB,OAAOC,IAAI,CAACT,YAAa;wBACzCxD,IAAIuB,SAAS,CAACmB,KAAKc,UAAU,CAACd,IAAI;oBACpC;gBACF;gBACA,MAAMwB,SAAS,MAAM7F,mBAAmBwE,cAAc,CAAC9C,KAAKC;gBAE5D,IAAIkE,OAAOX,QAAQ,EAAE;oBACnB;gBACF;gBACA,sEAAsE;gBACtExD,IAAIpF,GAAG,GAAGuI;YACZ;YAEA/F,MAAM,mBAAmB4C,IAAIpF,GAAG,EAAE;gBAChC+I;gBACAlC;gBACAgC;gBACAC,YAAY,CAAC,CAACA;gBACdxC,WAAW;oBACT5D,UAAU4D,UAAU5D,QAAQ;oBAC5BoF,OAAOxB,UAAUwB,KAAK;gBACxB;gBACAc;YACF;YAEA,0CAA0C;YAC1C,KAAK,MAAMb,OAAOsB,OAAOC,IAAI,CAACT,cAAc,CAAC,GAAI;gBAC/CxD,IAAIuB,SAAS,CAACmB,KAAKc,UAAU,CAACd,IAAI;YACpC;YAEA,kBAAkB;YAClB,IAAI,CAACe,cAAcjC,cAAcA,aAAa,OAAOA,aAAa,KAAK;gBACrE,MAAM2C,cAAcxJ,IAAIyJ,MAAM,CAACnD;gBAC/BjB,IAAIwB,UAAU,GAAGA;gBACjBxB,IAAIuB,SAAS,CAAC,YAAY4C;gBAE1B,IAAI3C,eAAenF,mBAAmBgI,iBAAiB,EAAE;oBACvDrE,IAAIuB,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE4C,aAAa;gBACjD;gBACA,OAAOnE,IAAI0B,GAAG,CAACyC;YACjB;YAEA,kCAAkC;YAClC,IAAIV,YAAY;gBACdzD,IAAIwB,UAAU,GAAGA,cAAc;gBAC/B,OAAO,MAAMlG,mBAAmBmI,YAAYzD;YAC9C;YAEA,IAAIuD,YAAYtC,UAAUqD,QAAQ,EAAE;oBAMhC7I;gBALF,OAAO,MAAML,aACX2E,KACAC,KACAiB,WACAsD,YACA9I,kBAAAA,eAAesE,KAAK,oCAApBtE,gBAAqC+I,eAAe,IACpD1G,OAAO2G,YAAY,CAACC,YAAY;YAEpC;YAEA,IAAIhB,CAAAA,iCAAAA,cAAeiB,MAAM,KAAIjB,cAAckB,QAAQ,EAAE;gBACnD,IACEnH,KAAKI,GAAG,IACPK,CAAAA,UAAU2G,QAAQ,CAACC,GAAG,CAACpB,cAAckB,QAAQ,KAC5C1G,UAAU6G,SAAS,CAACD,GAAG,CAACpB,cAAckB,QAAQ,CAAA,GAChD;oBACA5E,IAAIwB,UAAU,GAAG;oBACjB,MAAMwD,UAAU,CAAC,2DAA2D,EAAEtB,cAAckB,QAAQ,CAAC,8DAA8D,CAAC;oBACpK,MAAM7C,aAAad,WAAW,WAAWgB,aAAa;wBACpDgD,cAAc;wBACdC,aAAa,qBAAkB,CAAlB,IAAI1C,MAAMwC,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAChC;oBACAhK,IAAImK,KAAK,CAACH;oBACV;gBACF;gBAEA,IACE,CAAChF,IAAIoF,SAAS,CAAC,oBACf1B,cAAcK,IAAI,KAAK,oBACvB;oBACA,IAAItG,KAAKI,GAAG,IAAI,CAACT,WAAW6D,UAAU5D,QAAQ,GAAG;wBAC/C2C,IAAIuB,SAAS,CAAC,iBAAiB;oBACjC,OAAO;wBACLvB,IAAIuB,SAAS,CACX,iBACA;oBAEJ;gBACF;gBACA,IAAI,CAAExB,CAAAA,IAAIsF,MAAM,KAAK,SAAStF,IAAIsF,MAAM,KAAK,MAAK,GAAI;oBACpDrF,IAAIuB,SAAS,CAAC,SAAS;wBAAC;wBAAO;qBAAO;oBACtCvB,IAAIwB,UAAU,GAAG;oBACjB,OAAO,MAAMO,aACXpH,IAAIwI,KAAK,CAAC,QAAQ,OAClB,QACAlB,aACA;wBACEgD,cAAc;oBAChB;gBAEJ;gBAEA,IAAI;oBACF,OAAO,MAAMnK,YAAYiF,KAAKC,KAAK0D,cAAckB,QAAQ,EAAE;wBACzDU,MAAM5B,cAAc6B,SAAS;wBAC7B,uEAAuE;wBACvEC,MAAM1H,OAAO2H,aAAa;oBAC5B;gBACF,EAAE,OAAO3C,KAAU;oBACjB;;;;;WAKC,GACD,MAAM4C,wCAAwC,IAAI5D,IAAI;wBACpD,kFAAkF;wBAClF,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,kDAAkD;wBAClD,+FAA+F;wBAC/F,mEAAmE;wBACnE,OAAO;wBAEP,gGAAgG;wBAChG,+FAA+F;wBAC/F,qFAAqF;wBACrF,OAAO;wBAEP,8DAA8D;wBAC9D,+FAA+F;wBAC/F;wBAEA,0DAA0D;wBAC1D,+FAA+F;wBAC/F;wBAEA,2DAA2D;wBAC3D,+FAA+F;wBAC/F;qBACD;oBAED,IAAI6D,mBAAmBD,sCAAsCZ,GAAG,CAC9DhC,IAAItB,UAAU;oBAGhB,qCAAqC;oBACrC,IAAI,CAACmE,kBAAkB;;wBACnB7C,IAAYtB,UAAU,GAAG;oBAC7B;oBAEA,IAAI,OAAOsB,IAAItB,UAAU,KAAK,UAAU;wBACtC,MAAMQ,aAAa,CAAC,CAAC,EAAEc,IAAItB,UAAU,EAAE;wBACvC,MAAMyD,eAAenC,IAAItB,UAAU;wBACnCxB,IAAIwB,UAAU,GAAGsB,IAAItB,UAAU;wBAC/B,OAAO,MAAMO,aACXpH,IAAIwI,KAAK,CAACnB,YAAY,OACtBA,YACAC,aACA;4BACEgD;wBACF;oBAEJ;oBACA,MAAMnC;gBACR;YACF;YAEA,IAAIY,eAAe;gBACjB7B,eAAe+D,GAAG,CAAClC,cAAckB,QAAQ;gBAEzC,OAAO,MAAM7C,aACXd,WACAA,UAAU5D,QAAQ,IAAI,KACtB4E,aACA;oBACE4D,cAAcnC,cAAckB,QAAQ;gBACtC;YAEJ;YAEA,WAAW;YACX5E,IAAIuB,SAAS,CACX,iBACA;YAGF,0IAA0I;YAC1I,IAAI9D,KAAKI,GAAG,IAAI,CAAC6F,iBAAiBzC,UAAU5D,QAAQ,KAAK,gBAAgB;gBACvE2C,IAAIwB,UAAU,GAAG;gBACjBxB,IAAI0B,GAAG,CAAC;gBACR,OAAO;YACT;YAEA,MAAMoE,cAAcrI,KAAKI,GAAG,GACxBQ,sCAAAA,mBAAoB0H,YAAY,CAACC,cAAc,GAC/C,MAAM9H,UAAU+H,OAAO,CAAC7J;YAE5B4D,IAAIwB,UAAU,GAAG;YAEjB,IAAIsE,aAAa;gBACf,OAAO,MAAM/D,aACXd,WACA7E,4BACA6F,aACA;oBACEgD,cAAc;gBAChB;YAEJ;YAEA,MAAMlD,aAAad,WAAW,QAAQgB,aAAa;gBACjDgD,cAAc;YAChB;QACF;QAEA,IAAI;YACF,MAAMlC,cAAc;QACtB,EAAE,OAAOD,KAAK;YACZ,IAAI;gBACF,IAAId,aAAa;gBACjB,IAAIiD,eAAe;gBAEnB,IAAInC,eAAe7H,aAAa;oBAC9B+G,aAAa;oBACbiD,eAAe;gBACjB,OAAO;oBACLiB,QAAQf,KAAK,CAACrC;gBAChB;gBACA9C,IAAIwB,UAAU,GAAG2E,OAAOlB;gBACxB,OAAO,MAAMlD,aAAapH,IAAIwI,KAAK,CAACnB,YAAY,OAAOA,YAAY,GAAG;oBACpEiD,cAAcjF,IAAIwB,UAAU;gBAC9B;YACF,EAAE,OAAO4E,MAAM;gBACbF,QAAQf,KAAK,CAACiB;YAChB;YACApG,IAAIwB,UAAU,GAAG;YACjBxB,IAAI0B,GAAG,CAAC;QACV;IACF;IAEA,IAAImB,iBAAuC3C;IAC3C,IAAIpC,OAAO2G,YAAY,CAAC4B,SAAS,EAAE;QACjC,2CAA2C;QAC3C,MAAM,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAE,GACnD5H,QAAQ;QACVkE,iBAAiByD,yBAAyBzD;QAC1C0D;QACA,yFAAyF;QACzFhI,gBAAgBC,WAAWC,KAAK;IAClC;IACAlB,eAAe,CAACE,KAAKM,GAAG,CAAC,GAAG8E;IAE5B,MAAMD,mBAA8D;QAClE/C,MAAMpC,KAAKoC,IAAI;QACf9B,KAAKN,KAAKM,GAAG;QACb+C,UAAUrD,KAAKqD,QAAQ;QACvB3C,aAAaV,KAAKU,WAAW;QAC7BN,KAAK,CAAC,CAACJ,KAAKI,GAAG;QACf2I,QAAQ/I,KAAK+I,MAAM;QACnBT,cAAc;YACZ,GAAI1H,CAAAA,sCAAAA,mBAAoB0H,YAAY,KAAI,CAAC,CAAC;YAC1CU,YAAY,EAAEnI,qCAAAA,kBAAmBmI,YAAY,CAACC,IAAI,CAACpI;QACrD;QACAqI,uBAAuB,CAAC,CAAC7I,OAAO2G,YAAY,CAAC4B,SAAS;QACtDO,yBAAyB,CAAC,CAACnJ,KAAKmJ,uBAAuB;QACvDC,gBAAgBvI;QAChBe,iBAAiB5B,KAAK4B,eAAe;QACrCyH,OAAOrJ,KAAKqJ,KAAK;QACjBhH,oBAAoBrC,KAAKqC,kBAAkB;IAC7C;IACA8C,iBAAiBmD,YAAY,CAACgB,mBAAmB,GAAG7G;IAEpD,yBAAyB;IACzB,MAAMqC,WAAW,MAAMnE,aAAa6B,QAAQ,CAACzC,UAAU,CAACoF;IAExD,MAAMoE,WAAW,OACfjD,MACAjB;QAEA,IAAI/G,WAAW+G,MAAM;YACnB,0EAA0E;YAC1E,qDAAqD;YACrD;QACF;QACA,IAAIiB,SAAS,sBAAsB;YACjC/I,IAAImK,KAAK,CAAC,wBAAwBrC;QACpC,OAAO,IAAIiB,SAAS,qBAAqB;YACvC/I,IAAImK,KAAK,CAAC,uBAAuBrC;QACnC;IACF;IAEApF,QAAQiE,EAAE,CAAC,qBAAqBqF,SAASN,IAAI,CAAC,MAAM;IACpDhJ,QAAQiE,EAAE,CAAC,sBAAsBqF,SAASN,IAAI,CAAC,MAAM;IAErD,MAAM/C,gBAAgBpI,iBACpB2C,WACAJ,QACAL,MACAW,aAAa6B,QAAQ,EACrB2C,kBACAvE,sCAAAA,mBAAoB4I,gBAAgB;IAGtC,MAAMC,iBAAuC,OAAOnH,KAAKoH,QAAQC;QAC/D,IAAI;YACFrH,IAAI4B,EAAE,CAAC,SAAS,CAACC;YACf,2BAA2B;YAC3B,uBAAuB;YACzB;YACAuF,OAAOxF,EAAE,CAAC,SAAS,CAACC;YAClB,2BAA2B;YAC3B,uBAAuB;YACzB;YAEA,IAAInE,KAAKI,GAAG,IAAIQ,sBAAsB0B,IAAIpF,GAAG,EAAE;gBAC7C,IACEsC,eAAe8C,KAAKoH,QAAQrJ,OAAOmF,iBAAiB,EAAExF,KAAKqD,QAAQ,GACnE;oBACA;gBACF;gBACA,MAAM,EAAEJ,QAAQ,EAAE2G,WAAW,EAAE,GAAGvJ;gBAElC,IAAIwJ,YAAY5G;gBAEhB,8CAA8C;gBAC9C,IAAI2G,aAAa;oBACfC,YAAYxK,sBAAsBuK;oBAElC,IAAIE,IAAIC,QAAQ,CAACF,YAAY;wBAC3B,sCAAsC;wBACtC,yCAAyC;wBACzC,yCAAyC;wBACzCA,YAAY,IAAIC,IAAID,WAAWjK,QAAQ,CAAC6D,OAAO,CAAC,OAAO;oBACzD;gBACF;gBAEA,MAAMuG,eAAe1H,IAAIpF,GAAG,CAACwH,UAAU,CACrC3F,mBAAmB,GAAG8K,UAAU,kBAAkB,CAAC;gBAGrD,0DAA0D;gBAC1D,iEAAiE;gBACjE,IAAIG,cAAc;oBAChB,OAAOpJ,mBAAmBgF,WAAW,CAACqE,KAAK,CACzC3H,KACAoH,QACAC,MACA,CAACO;wBACCA,OAAOC,IAAI,CACTC,KAAKC,SAAS,CAAC;4BACbC,QAAQlL,4BAA4BmL,YAAY;4BAChDC,MAAM3J,CAAAA,qCAAAA,kBAAmB4J,cAAc,KAAI,CAAC;wBAC9C;oBAEJ;gBAEJ;YACF;YAEA,MAAMlI,MAAM,IAAIpD,eAAe;gBAC7BuL,WAAW;oBACT,MAAM,qBAEL,CAFK,IAAI3F,MACR,mFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YACA,MAAM,EAAEkB,aAAa,EAAEzC,SAAS,EAAE,GAAG,MAAM0C,cAAc;gBACvD5D;gBACAC;gBACA4D,cAAc;gBACdC,QAAQ/H,uBAAuBqL;YACjC;YAEA,mDAAmD;YACnD,oCAAoC;YACpC,IAAIzD,eAAe;gBACjB,OAAOyD,OAAOzF,GAAG;YACnB;YAEA,IAAIT,UAAUqD,QAAQ,EAAE;gBACtB,OAAO,MAAMlJ,aAAa2E,KAAKoH,QAAQlG,WAAWmG;YACpD;QAEA,sEAAsE;QACtE,sDAAsD;QACxD,EAAE,OAAOtE,KAAK;YACZoD,QAAQf,KAAK,CAAC,kCAAkCrC;YAChDqE,OAAOzF,GAAG;QACZ;IACF;IAEA,OAAO;QACLmB;QACAqE;QACAV,QAAQjE,SAASiE,MAAM;QACvB4B;gBACE/J;YAAAA,uCAAAA,kCAAAA,mBAAoBgF,WAAW,qBAA/BhF,gCAAiCgK,KAAK;QACxC;IACF;AACF"}