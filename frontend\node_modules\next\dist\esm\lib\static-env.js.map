{"version": 3, "sources": ["../../src/lib/static-env.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\n\nfunction errorIfEnvConflicted(config: NextConfigComplete, key: string) {\n  const isPrivateKey = /^(?:NODE_.+)|^(?:__.+)$/i.test(key)\n  const hasNextRuntimeKey = key === 'NEXT_RUNTIME'\n\n  if (isPrivateKey || hasNextRuntimeKey) {\n    throw new Error(\n      `The key \"${key}\" under \"env\" in ${config.configFileName} is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n    )\n  }\n}\n\n/**\n * Collects all environment variables that are using the `NEXT_PUBLIC_` prefix.\n */\nexport function getNextPublicEnvironmentVariables() {\n  const defineEnv: Record<string, string | undefined> = {}\n  for (const key in process.env) {\n    if (key.startsWith('NEXT_PUBLIC_')) {\n      const value = process.env[key]\n      if (value != null) {\n        defineEnv[`process.env.${key}`] = value\n      }\n    }\n  }\n  return defineEnv\n}\n\n/**\n * Collects the `env` config value from the Next.js config.\n */\nexport function getNextConfigEnv(config: NextConfigComplete) {\n  // Refactored code below to use for-of\n  const defineEnv: Record<string, string | undefined> = {}\n  const env = config.env\n  for (const key in env) {\n    const value = env[key]\n    if (value != null) {\n      errorIfEnvConflicted(config, key)\n      defineEnv[`process.env.${key}`] = value\n    }\n  }\n  return defineEnv\n}\n\nexport function getStaticEnv(config: NextConfigComplete) {\n  const staticEnv: Record<string, string | undefined> = {\n    ...getNextPublicEnvironmentVariables(),\n    ...getNextConfigEnv(config),\n    'process.env.NEXT_DEPLOYMENT_ID': config.deploymentId || '',\n  }\n  return staticEnv\n}\n\nexport function populateStaticEnv(config: NextConfigComplete) {\n  // since inlining comes after static generation we need\n  // to ensure this value is assigned to process env so it\n  // can still be accessed\n  const staticEnv = getStaticEnv(config)\n  for (const key in staticEnv) {\n    const innerKey = key.split('.').pop() || ''\n    if (!process.env[innerKey]) {\n      process.env[innerKey] = staticEnv[key] || ''\n    }\n  }\n}\n"], "names": ["errorIfEnvConflicted", "config", "key", "isPrivateKey", "test", "hasNextRuntimeKey", "Error", "configFileName", "getNextPublicEnvironmentVariables", "defineEnv", "process", "env", "startsWith", "value", "getNextConfigEnv", "getStaticEnv", "staticEnv", "deploymentId", "populateStaticEnv", "innerKey", "split", "pop"], "mappings": "AAEA,SAASA,qBAAqBC,MAA0B,EAAEC,GAAW;IACnE,MAAMC,eAAe,2BAA2BC,IAAI,CAACF;IACrD,MAAMG,oBAAoBH,QAAQ;IAElC,IAAIC,gBAAgBE,mBAAmB;QACrC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,SAAS,EAAEJ,IAAI,iBAAiB,EAAED,OAAOM,cAAc,CAAC,qEAAqE,CAAC,GAD3H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEA;;CAEC,GACD,OAAO,SAASC;IACd,MAAMC,YAAgD,CAAC;IACvD,IAAK,MAAMP,OAAOQ,QAAQC,GAAG,CAAE;QAC7B,IAAIT,IAAIU,UAAU,CAAC,iBAAiB;YAClC,MAAMC,QAAQH,QAAQC,GAAG,CAACT,IAAI;YAC9B,IAAIW,SAAS,MAAM;gBACjBJ,SAAS,CAAC,CAAC,YAAY,EAAEP,KAAK,CAAC,GAAGW;YACpC;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,OAAO,SAASK,iBAAiBb,MAA0B;IACzD,sCAAsC;IACtC,MAAMQ,YAAgD,CAAC;IACvD,MAAME,MAAMV,OAAOU,GAAG;IACtB,IAAK,MAAMT,OAAOS,IAAK;QACrB,MAAME,QAAQF,GAAG,CAACT,IAAI;QACtB,IAAIW,SAAS,MAAM;YACjBb,qBAAqBC,QAAQC;YAC7BO,SAAS,CAAC,CAAC,YAAY,EAAEP,KAAK,CAAC,GAAGW;QACpC;IACF;IACA,OAAOJ;AACT;AAEA,OAAO,SAASM,aAAad,MAA0B;IACrD,MAAMe,YAAgD;QACpD,GAAGR,mCAAmC;QACtC,GAAGM,iBAAiBb,OAAO;QAC3B,kCAAkCA,OAAOgB,YAAY,IAAI;IAC3D;IACA,OAAOD;AACT;AAEA,OAAO,SAASE,kBAAkBjB,MAA0B;IAC1D,uDAAuD;IACvD,wDAAwD;IACxD,wBAAwB;IACxB,MAAMe,YAAYD,aAAad;IAC/B,IAAK,MAAMC,OAAOc,UAAW;QAC3B,MAAMG,WAAWjB,IAAIkB,KAAK,CAAC,KAAKC,GAAG,MAAM;QACzC,IAAI,CAACX,QAAQC,GAAG,CAACQ,SAAS,EAAE;YAC1BT,QAAQC,GAAG,CAACQ,SAAS,GAAGH,SAAS,CAACd,IAAI,IAAI;QAC5C;IACF;AACF"}