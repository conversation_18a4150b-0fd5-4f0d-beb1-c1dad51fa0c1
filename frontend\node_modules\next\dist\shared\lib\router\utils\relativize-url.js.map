{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/relativize-url.ts"], "sourcesContent": ["/**\n * The result of parsing a URL relative to a base URL.\n */\nexport type RelativeURL = {\n  /**\n   * The relative URL. Either a URL including the origin or a relative URL.\n   */\n  url: string\n\n  /**\n   * Whether the URL is relative to the base URL.\n   */\n  isRelative: boolean\n}\n\nexport function parseRelativeURL(\n  url: string | URL,\n  base: string | URL\n): RelativeURL {\n  const baseURL = typeof base === 'string' ? new URL(base) : base\n  const relative = new URL(url, base)\n\n  // The URL is relative if the origin is the same as the base URL.\n  const isRelative = relative.origin === baseURL.origin\n\n  return {\n    url: isRelative\n      ? relative.toString().slice(baseURL.origin.length)\n      : relative.toString(),\n    isRelative,\n  }\n}\n\n/**\n * Given a URL as a string and a base URL it will make the URL relative\n * if the parsed protocol and host is the same as the one in the base\n * URL. Otherwise it returns the same URL string.\n */\nexport function getRelativeURL(url: string | URL, base: string | URL): string {\n  const relative = parseRelativeURL(url, base)\n  return relative.url\n}\n"], "names": ["getRelativeURL", "parseRelativeURL", "url", "base", "baseURL", "URL", "relative", "isRelative", "origin", "toString", "slice", "length"], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;IAoCeA,cAAc;eAAdA;;IAvBAC,gBAAgB;eAAhBA;;;AAAT,SAASA,iBACdC,GAAiB,EACjBC,IAAkB;IAElB,MAAMC,UAAU,OAAOD,SAAS,WAAW,IAAIE,IAAIF,QAAQA;IAC3D,MAAMG,WAAW,IAAID,IAAIH,KAAKC;IAE9B,iEAAiE;IACjE,MAAMI,aAAaD,SAASE,MAAM,KAAKJ,QAAQI,MAAM;IAErD,OAAO;QACLN,KAAKK,aACDD,SAASG,QAAQ,GAAGC,KAAK,CAACN,QAAQI,MAAM,CAACG,MAAM,IAC/CL,SAASG,QAAQ;QACrBF;IACF;AACF;AAOO,SAASP,eAAeE,GAAiB,EAAEC,IAAkB;IAClE,MAAMG,WAAWL,iBAAiBC,KAAKC;IACvC,OAAOG,SAASJ,GAAG;AACrB"}