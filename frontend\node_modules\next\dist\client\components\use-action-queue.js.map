{"version": 3, "sources": ["../../../src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const useSyncDevRenderIndicator =\n      require('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator')\n        .useSyncDevRenderIndicator as typeof import('./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator').useSyncDevRenderIndicator\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const syncDevRenderIndicator = useSyncDevRenderIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      syncDevRenderIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n"], "names": ["dispatchAppRouterAction", "useActionQueue", "dispatch", "action", "Error", "actionQueue", "state", "setState", "React", "useState", "process", "env", "NODE_ENV", "useSyncDevRenderIndicator", "require", "syncDevRenderIndicator", "isThenable", "use"], "mappings": ";;;;;;;;;;;;;;;IAegBA,uBAAuB;eAAvBA;;IASAC,cAAc;eAAdA;;;;iEAvBW;4BACA;AAQ3B,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASF,wBAAwBG,MAAsB;IAC5D,IAAID,aAAa,MAAM;QACrB,MAAM,qBAEL,CAFK,IAAIE,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAF,SAASC;AACX;AAEO,SAASF,eACdI,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,GAAGC,cAAK,CAACC,QAAQ,CAAeJ,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,4BACJC,QAAQ,yEACLD,yBAAyB;QAC9B,sDAAsD;QACtD,MAAME,yBAAyBF;QAE/BX,WAAW,CAACC;YACVY,uBAAuB;gBACrBV,YAAYH,QAAQ,CAACC,QAAQI;YAC/B;QACF;IACF,OAAO;QACLL,WAAW,CAACC,SACVE,YAAYH,QAAQ,CAACC,QAAQI;IACjC;IAEA,OAAOS,IAAAA,sBAAU,EAACV,SAASW,IAAAA,UAAG,EAACX,SAASA;AAC1C"}