{"version": 3, "sources": ["../../../../src/build/webpack/plugins/subresource-integrity-plugin.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport crypto from 'crypto'\nimport { SUBRESOURCE_INTEGRITY_MANIFEST } from '../../../shared/lib/constants'\n\nconst PLUGIN_NAME = 'SubresourceIntegrityPlugin'\n\nexport type SubresourceIntegrityAlgorithm = 'sha256' | 'sha384' | 'sha512'\n\nexport class SubresourceIntegrityPlugin {\n  constructor(private readonly algorithm: SubresourceIntegrityAlgorithm) {}\n\n  public apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.afterProcessAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          // Collect all the assets.\n          let files = new Set<string>()\n          for (const asset of compilation.getAssets()) {\n            files.add(asset.name)\n          }\n\n          // For each file, deduped, calculate the file hash.\n          const hashes: Record<string, string> = {}\n          for (const file of files.values()) {\n            // Get the buffer for the asset.\n            const asset = compilation.getAsset(file)\n            if (!asset) {\n              throw new Error(`could not get asset: ${file}`)\n            }\n\n            // Get the buffer for the asset.\n            const buffer = asset.source.buffer()\n\n            // Create the hash for the content.\n            const hash = crypto\n              .createHash(this.algorithm)\n              .update(buffer)\n              .digest()\n              .toString('base64')\n\n            hashes[file] = `${this.algorithm}-${hash}`\n          }\n\n          const json = JSON.stringify(hashes, null, 2)\n          const file = 'server/' + SUBRESOURCE_INTEGRITY_MANIFEST\n          compilation.emitAsset(\n            file + '.js',\n            new sources.RawSource(\n              `self.__SUBRESOURCE_INTEGRITY_MANIFEST=${JSON.stringify(json)}`\n              // Work around webpack 4 type of RawSource being used\n              // TODO: use webpack 5 type by default\n            ) as unknown as webpack.sources.RawSource\n          )\n          compilation.emitAsset(\n            file + '.json',\n            new sources.RawSource(\n              json\n              // Work around webpack 4 type of RawSource being used\n              // TODO: use webpack 5 type by default\n            ) as unknown as webpack.sources.RawSource\n          )\n        }\n      )\n    })\n  }\n}\n"], "names": ["webpack", "sources", "crypto", "SUBRESOURCE_INTEGRITY_MANIFEST", "PLUGIN_NAME", "SubresourceIntegrityPlugin", "constructor", "algorithm", "apply", "compiler", "hooks", "make", "tap", "compilation", "afterProcessAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "files", "Set", "asset", "getAssets", "add", "hashes", "file", "values", "getAsset", "Error", "buffer", "source", "hash", "createHash", "update", "digest", "toString", "json", "JSON", "stringify", "emitAsset", "RawSource"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,YAAY,SAAQ;AAC3B,SAASC,8BAA8B,QAAQ,gCAA+B;AAE9E,MAAMC,cAAc;AAIpB,OAAO,MAAMC;IACXC,YAAY,AAAiBC,SAAwC,CAAE;aAA1CA,YAAAA;IAA2C;IAEjEC,MAAMC,QAA0B,EAAE;QACvCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACR,aAAa,CAACS;YACpCA,YAAYH,KAAK,CAACI,kBAAkB,CAACF,GAAG,CACtC;gBACEG,MAAMX;gBACNY,OAAOhB,QAAQiB,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,0BAA0B;gBAC1B,IAAIC,QAAQ,IAAIC;gBAChB,KAAK,MAAMC,SAASR,YAAYS,SAAS,GAAI;oBAC3CH,MAAMI,GAAG,CAACF,MAAMN,IAAI;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMS,SAAiC,CAAC;gBACxC,KAAK,MAAMC,QAAQN,MAAMO,MAAM,GAAI;oBACjC,gCAAgC;oBAChC,MAAML,QAAQR,YAAYc,QAAQ,CAACF;oBACnC,IAAI,CAACJ,OAAO;wBACV,MAAM,qBAAyC,CAAzC,IAAIO,MAAM,CAAC,qBAAqB,EAAEH,MAAM,GAAxC,qBAAA;mCAAA;wCAAA;0CAAA;wBAAwC;oBAChD;oBAEA,gCAAgC;oBAChC,MAAMI,SAASR,MAAMS,MAAM,CAACD,MAAM;oBAElC,mCAAmC;oBACnC,MAAME,OAAO7B,OACV8B,UAAU,CAAC,IAAI,CAACzB,SAAS,EACzB0B,MAAM,CAACJ,QACPK,MAAM,GACNC,QAAQ,CAAC;oBAEZX,MAAM,CAACC,KAAK,GAAG,GAAG,IAAI,CAAClB,SAAS,CAAC,CAAC,EAAEwB,MAAM;gBAC5C;gBAEA,MAAMK,OAAOC,KAAKC,SAAS,CAACd,QAAQ,MAAM;gBAC1C,MAAMC,OAAO,YAAYtB;gBACzBU,YAAY0B,SAAS,CACnBd,OAAO,OACP,IAAIxB,QAAQuC,SAAS,CACnB,CAAC,sCAAsC,EAAEH,KAAKC,SAAS,CAACF,OAAO;gBAKnEvB,YAAY0B,SAAS,CACnBd,OAAO,SACP,IAAIxB,QAAQuC,SAAS,CACnBJ;YAKN;QAEJ;IACF;AACF"}