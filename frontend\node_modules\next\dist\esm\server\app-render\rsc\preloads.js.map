{"version": 3, "sources": ["../../../../src/server/app-render/rsc/preloads.ts"], "sourcesContent": ["/*\n\nFiles in the rsc directory are meant to be packaged as part of the RSC graph using next-app-loader.\n\n*/\n\nimport ReactDOM from 'react-dom'\n\nexport function preloadStyle(\n  href: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = { as: 'style' }\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ReactDOM.preload(href, opts)\n}\n\nexport function preloadFont(\n  href: string,\n  type: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = { as: 'font', type }\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ReactDOM.preload(href, opts)\n}\n\nexport function preconnect(\n  href: string,\n  crossOrigin: string | undefined,\n  nonce: string | undefined\n) {\n  const opts: any = {}\n  if (typeof crossOrigin === 'string') {\n    opts.crossOrigin = crossOrigin\n  }\n  if (typeof nonce === 'string') {\n    opts.nonce = nonce\n  }\n  ;(ReactDOM as any).preconnect(href, opts)\n}\n"], "names": ["ReactDOM", "preloadStyle", "href", "crossOrigin", "nonce", "opts", "as", "preload", "preloadFont", "type", "preconnect"], "mappings": "AAAA;;;;AAIA,GAEA,OAAOA,cAAc,YAAW;AAEhC,OAAO,SAASC,aACdC,IAAY,EACZC,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY;QAAEC,IAAI;IAAQ;IAChC,IAAI,OAAOH,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;IACAJ,SAASO,OAAO,CAACL,MAAMG;AACzB;AAEA,OAAO,SAASG,YACdN,IAAY,EACZO,IAAY,EACZN,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY;QAAEC,IAAI;QAAQG;IAAK;IACrC,IAAI,OAAON,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;IACAJ,SAASO,OAAO,CAACL,MAAMG;AACzB;AAEA,OAAO,SAASK,WACdR,IAAY,EACZC,WAA+B,EAC/BC,KAAyB;IAEzB,MAAMC,OAAY,CAAC;IACnB,IAAI,OAAOF,gBAAgB,UAAU;QACnCE,KAAKF,WAAW,GAAGA;IACrB;IACA,IAAI,OAAOC,UAAU,UAAU;QAC7BC,KAAKD,KAAK,GAAGA;IACf;;IACEJ,SAAiBU,UAAU,CAACR,MAAMG;AACtC"}