{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "sourcesContent": ["import { createHrefFromUrl } from '../create-href-from-url'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  RestoreAction,\n} from '../router-reducer-types'\nimport { extractPathFromFlightRouterState } from '../compute-changed-path'\nimport { updateCacheNodeOnPopstateRestoration } from '../ppr-navigations'\n\nexport function restoreReducer(\n  state: ReadonlyReducerState,\n  action: RestoreAction\n): ReducerState {\n  const { url, tree } = action\n  const href = createHrefFromUrl(url)\n  // This action is used to restore the router state from the history state.\n  // However, it's possible that the history state no longer contains the `FlightRouterState`.\n  // We will copy over the internal state on pushState/replaceState events, but if a history entry\n  // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n  // the history state will not contain the `FlightRouterState`.\n  // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n  const treeToRestore = tree || state.tree\n\n  const oldCache = state.cache\n  const newCache = process.env.__NEXT_PPR\n    ? // When PPR is enabled, we update the cache to drop the prefetch\n      // data for any segment whose dynamic data was already received. This\n      // prevents an unnecessary flash back to PPR state during a\n      // back/forward navigation.\n      updateCacheNodeOnPopstateRestoration(oldCache, treeToRestore)\n    : oldCache\n\n  return {\n    // Set canonical url\n    canonicalUrl: href,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // Ensures that the custom history state that was set is preserved when applying this update.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: state.focusAndScrollRef,\n    cache: newCache,\n    prefetchCache: state.prefetchCache,\n    // Restore provided tree\n    tree: treeToRestore,\n    nextUrl: extractPathFromFlightRouterState(treeToRestore) ?? url.pathname,\n  }\n}\n"], "names": ["restoreReducer", "state", "action", "url", "tree", "href", "createHrefFromUrl", "treeToRestore", "<PERSON><PERSON><PERSON>", "cache", "newCache", "process", "env", "__NEXT_PPR", "updateCacheNodeOnPopstateRestoration", "extractPathFromFlightRouterState", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "prefetchCache", "nextUrl", "pathname"], "mappings": ";;;;+BASgBA;;;eAAAA;;;mCATkB;oCAMe;gCACI;AAE9C,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOC,IAAAA,oCAAiB,EAACH;IAC/B,0EAA0E;IAC1E,4FAA4F;IAC5F,gGAAgG;IAChG,6FAA6F;IAC7F,8DAA8D;IAC9D,yGAAyG;IACzG,MAAMI,gBAAgBH,QAAQH,MAAMG,IAAI;IAExC,MAAMI,WAAWP,MAAMQ,KAAK;IAC5B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,UAAU,GAEnC,qEAAqE;IACrE,2DAA2D;IAC3D,2BAA2B;IAC3BC,IAAAA,oDAAoC,EAACN,UAAUD,iBAC/CC;QAgBOO;IAdX,OAAO;QACL,oBAAoB;QACpBC,cAAcX;QACdY,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBpB,MAAMoB,iBAAiB;QAC1CZ,OAAOC;QACPY,eAAerB,MAAMqB,aAAa;QAClC,wBAAwB;QACxBlB,MAAMG;QACNgB,SAASR,CAAAA,oCAAAA,IAAAA,oDAAgC,EAACR,0BAAjCQ,oCAAmDZ,IAAIqB,QAAQ;IAC1E;AACF"}