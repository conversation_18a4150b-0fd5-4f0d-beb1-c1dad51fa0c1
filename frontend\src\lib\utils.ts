import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export async function fetcher<T>(
    url: string,
    values: T,
    method: "GET" | "POST" = "GET",
    token?: string
) {
    const res = await fetch(process.env.NEXT_PUBLIC_BACKEND_URL + url, {
        method: method,
        body: values === null ? null : JSON.stringify(values),
        headers: {
            "Content-Type": "application/json",
            Authorization: token ? `Bearer ${token}` : "",
        },
    });
    return res;
}

export const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
    });
};
