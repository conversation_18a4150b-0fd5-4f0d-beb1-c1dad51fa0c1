(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[982],{35695:(e,s,t)=>{"use strict";var n=t(18999);t.o(n,"notFound")&&t.d(s,{notFound:function(){return n.notFound}}),t.o(n,"useParams")&&t.d(s,{useParams:function(){return n.useParams}}),t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(s,{useSearchParams:function(){return n.useSearchParams}})},36912:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h,dynamic:()=>u});var n=t(95155),r=t(12115),a=t(35695),o=t(82842),c=t(56671);let u="force-dynamic";function l(){let e=(0,a.useRouter)(),[,s]=(0,o.lT)(),t=(0,a.useSearchParams)().get("code");return((0,r.useEffect)(()=>{t&&fetch("".concat("http://localhost:8000","/auth/google/"),{method:"POST",body:JSON.stringify({code:t}),headers:{"Content-Type":"application/json",accept:"application/json"}}).then(s=>{if(201===s.status)return s.json();c.o.error("فشل تسجيل الدخول، برجاء اعادة المحاولة"),e.push("/auth")}).then(t=>{s("access",t.access_token),s("refresh",t.refresh_token),c.o.success("تم تسجيل الدخول بنجاح"),e.refresh(),e.push("/")})},[t,e,s]),t)?(0,n.jsx)("div",{className:"grid place-content-center h-full",children:(0,n.jsx)("p",{className:"font-bold text-2xl",children:"جارى تسجيل الدخول..."})}):(0,a.notFound)()}function h(){return(0,n.jsx)(r.Suspense,{fallback:(0,n.jsx)("div",{className:"grid place-content-center h-full",children:(0,n.jsx)("p",{className:"font-bold text-2xl",children:"جارى التحميل..."})}),children:(0,n.jsx)(l,{})})}},49328:(e,s,t)=>{Promise.resolve().then(t.bind(t,36912))}},e=>{var s=s=>e(e.s=s);e.O(0,[671,842,441,684,358],()=>s(49328)),_N_E=e.O()}]);