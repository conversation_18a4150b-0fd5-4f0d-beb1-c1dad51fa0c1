"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[168],{23905:(t,e,i)=>{i.d(e,{$:()=>G});var s=i(52290),o=i(19827),r=i(54542),n=i(46256),a=i(47215),l=i(51442),h=i(51586);function u(t,e,i,s){return(0,l.k)(t,e,(0,h.F)(i),s)}let d=(t,e)=>Math.abs(t-e);var c=i(97007),m=i(59210);class p{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=y(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(d(t.x,e.x)**2+d(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:o}=m.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:n}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),n&&n(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=g(e,this.transformPagePoint),m.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:o}=this.handlers;if(this.dragSnapToOrigin&&o&&o(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=y("pointercancel"===t.type?this.lastMoveEventInfo:g(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!(0,n.Mc)(t))return;this.dragSnapToOrigin=o,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=g((0,h.e)(t),this.transformPagePoint),{point:a}=r,{timestamp:l}=m.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:p}=e;p&&p(t,y(r,this.history)),this.removeListeners=(0,c.F)(u(this.contextWindow,"pointermove",this.handlePointerMove),u(this.contextWindow,"pointerup",this.handlePointerUp),u(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,m.WG)(this.updatePoint)}}function g(t,e){return e?{point:e(t.point)}:t}function v(t,e){return{x:t.x-e.x,y:t.y-e.y}}function y({point:t},e){return{point:t,delta:v(t,f(e)),offset:v(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,o=f(t);for(;i>=0&&(s=t[i],!(o.timestamp-s.timestamp>(0,a.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,a.X)(o.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let n={x:(o.x-s.x)/r,y:(o.y-s.y)/r};return n.x===1/0&&(n.x=0),n.y===1/0&&(n.y=0),n}(e,.1)}}function f(t){return t[t.length-1]}var x=i(33991),P=i(45818),T=i(64200),D=i(77782),A=i(21109);function E(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function R(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function w(t,e,i){return{min:S(t,e),max:S(t,i)}}function S(t,e){return"number"==typeof t?t:t[e]||0}var j=i(81786),k=i(94198),L=i(33757),C=i(78588),B=i(45471),V=i(54228);let M=({current:t})=>t?t.ownerDocument.defaultView:null;var b=i(76333);let U=new WeakMap;class F{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,j.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new p(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,h.e)(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:o}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,n.Wp)(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,k.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(B.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=(0,T.CQ)(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),o&&m.Gt.postRender(()=>o(t,e)),(0,b.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:o,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:n}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(n),null!==this.currentDirection&&o&&o(this.currentDirection);return}this.updateAxis("x",e.point,n),this.updateAxis("y",e.point,n),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,k.X)(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:M(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&m.Gt.postRender(()=>o(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!O(t,s,this.currentDirection))return;let o=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,A.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,A.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),o.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,o=this.constraints;e&&(0,x.X)(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:o}){return{x:E(t.x,i,o),y:E(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:w(t,"left","right"),y:w(t,"top","bottom")}}(i),o!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&(0,k.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!(0,x.X)(e))return!1;let s=e.current;(0,r.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let n=(0,L.L)(s,o.root,this.visualElement.getTransformPagePoint()),a=(t=o.layout.layoutBox,{x:R(t.x,n.x),y:R(t.y,n.y)});if(i){let t=i((0,C.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,C.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:n}=this.getProps(),a=this.constraints||{};return Promise.all((0,k.X)(n=>{if(!O(n,e,this.currentDirection))return;let l=a&&a[n]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[n]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...o,...l};return this.startAxisValueAnimation(n,h)})).then(n)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,b.g)(this.visualElement,t),i.start((0,V.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){(0,k.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,k.X)(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){(0,k.X)(e=>{let{drag:i}=this.getProps();if(!O(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,o=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];o.set(t[e]-(0,A.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!(0,x.X)(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};(0,k.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=(0,T.CQ)(t),o=(0,T.CQ)(e);return o>s?i=(0,P.q)(e.min,e.max-s,t.min):s>o&&(i=(0,P.q)(t.min,t.max-o,e.min)),(0,D.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),(0,k.X)(e=>{if(!O(e,t,null))return;let i=this.getAxisMotionValue(e),{min:o,max:r}=this.constraints[e];i.set((0,A.k)(o,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;U.set(this.visualElement,this);let t=u(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();(0,x.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.Gt.read(e);let o=(0,l.k)(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,k.X)(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{o(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:r=.35,dragMomentum:n=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:o,dragElastic:r,dragMomentum:n}}}function O(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class N extends s.X{constructor(t){super(t),this.removeGroupControls=o.l,this.removeListeners=o.l,this.controls=new F(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||o.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let $=t=>(e,i)=>{t&&m.Gt.postRender(()=>t(e,i))};class W extends s.X{constructor(){super(...arguments),this.removePointerDownListener=o.l}onPointerDown(t){this.session=new p(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:M(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:$(t),onStart:$(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&m.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=u(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var X=i(49267);let G={pan:{Feature:W},drag:{Feature:N,ProjectionNode:i(55607).P,MeasureLayout:X.$}}},29901:(t,e,i)=>{i.d(e,{w:()=>s});let s={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},40157:(t,e,i)=>{i.d(e,{A:()=>l});var s=i(12115);let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,s.forwardRef)((t,e)=>{let{color:i="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:h="",children:u,iconNode:d,...c}=t;return(0,s.createElement)("svg",{ref:e,...n,width:o,height:o,stroke:i,strokeWidth:l?24*Number(a)/Number(o):a,className:r("lucide",h),...c},[...d.map(t=>{let[e,i]=t;return(0,s.createElement)(e,i)}),...Array.isArray(u)?u:[u]])}),l=(t,e)=>{let i=(0,s.forwardRef)((i,n)=>{let{className:l,...h}=i;return(0,s.createElement)(a,{ref:n,iconNode:e,className:r("lucide-".concat(o(t)),l),...h})});return i.displayName="".concat(t),i}},49267:(t,e,i)=>{i.d(e,{$:()=>f});var s=i(95155),o=i(12115),r=i(32082),n=i(90869),a=i(70797),l=i(29901),h=i(45471);function u(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let d={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!h.px.test(t))return t;else t=parseFloat(t);let i=u(t,e.target.x),s=u(t,e.target.y);return`${i}% ${s}%`}};var c=i(21109),m=i(93013),p=i(20637),g=i(48466),v=i(59210);class y extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:o}=t;(0,p.$)(x),o&&(e.group&&e.group.add(o),i&&i.register&&s&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),l.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:o}=this.props,r=i.projection;return r&&(r.isPresent=o,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?r.promote():r.relegate()||v.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),g.k.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function f(t){let[e,i]=(0,r.xQ)(),l=(0,o.useContext)(n.L);return(0,s.jsx)(y,{...t,layoutGroup:l,switchLayoutGroup:(0,o.useContext)(a.N),isPresent:e,safeToRemove:i})}let x={borderRadius:{...d,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d,borderTopRightRadius:d,borderBottomLeftRadius:d,borderBottomRightRadius:d,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=m.f.parse(t);if(s.length>5)return t;let o=m.f.createTransformer(t),r=+("number"!=typeof s[0]),n=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=n,s[1+r]/=a;let l=(0,c.k)(n,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),o(s)}}}},53292:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(55607),o=i(49267);let r={layout:{ProjectionNode:s.P,MeasureLayout:o.$}}},55607:(t,e,i)=>{i.d(e,{P:()=>tC});var s=i(46256),o=i(19827),r=i(59779),n=i(14570),a=i(54228),l=i(46926),h=i(59210),u=i(48466),d=i(19932),c=i(63284);let m=(t,e)=>t.depth-e.depth;class p{constructor(){this.children=[],this.isDirty=!1}add(t){(0,c.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,c.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(m),this.isDirty=!1,this.children.forEach(t)}}var g=i(77782),v=i(21109),y=i(66802),f=i(95902),x=i(45818),P=i(59282),T=i(45471);let D=["TopLeft","TopRight","BottomLeft","BottomRight"],A=D.length,E=t=>"string"==typeof t?parseFloat(t):t,R=t=>"number"==typeof t||T.px.test(t);function w(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let S=k(0,.5,P.yT),j=k(.5,.95,o.l);function k(t,e,i){return s=>s<t?0:s>e?1:i((0,x.q)(t,e,s))}function L(t,e){t.min=e.min,t.max=e.max}function C(t,e){L(t.x,e.x),L(t.y,e.y)}function B(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var V=i(96147),M=i(64200);function b(t,e,i,s,o){return t-=e,t=(0,V.hq)(t,1/i,s),void 0!==o&&(t=(0,V.hq)(t,1/o,s)),t}function U(t,e,[i,s,o],r,n){!function(t,e=0,i=1,s=.5,o,r=t,n=t){if(T.KN.test(e)&&(e=parseFloat(e),e=(0,v.k)(n.min,n.max,e/100)-n.min),"number"!=typeof e)return;let a=(0,v.k)(r.min,r.max,s);t===r&&(a-=e),t.min=b(t.min,e,i,a,o),t.max=b(t.max,e,i,a,o)}(t,e[i],e[s],e[o],e.scale,r,n)}let F=["x","scaleX","originX"],O=["y","scaleY","originY"];function N(t,e,i,s){U(t.x,e,F,i?i.x:void 0,s?s.x:void 0),U(t.y,e,O,i?i.y:void 0,s?s.y:void 0)}var $=i(81786);function W(t){return 0===t.translate&&1===t.scale}function X(t){return W(t.x)&&W(t.y)}function G(t,e){return t.min===e.min&&t.max===e.max}function H(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function I(t,e){return H(t.x,e.x)&&H(t.y,e.y)}function z(t){return(0,M.CQ)(t.x)/(0,M.CQ)(t.y)}function Q(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class q{constructor(){this.members=[]}add(t){(0,c.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,c.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var Y=i(20637),K=i(94198),_=i(62662),Z=i(29901);let J={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},tt="undefined"!=typeof window&&void 0!==window.MotionDebug,te=["","X","Y","Z"],ti={visibility:"hidden"},ts=0;function to(t,e,i,s){let{latestValues:o}=e;o[t]&&(i[t]=o[t],e.setStaticValue(t,0),s&&(s[t]=0))}function tr({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:o,resetTransform:c}){return class{constructor(t={},i=null==e?void 0:e()){this.id=ts++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tt&&(J.totalNodes=J.resolvedTargetDeltas=J.recalculatedProjection=0),this.nodes.forEach(tl),this.nodes.forEach(tg),this.nodes.forEach(tv),this.nodes.forEach(th),tt&&window.MotionDebug.record(J)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new p)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new y.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:o,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||o)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=d.k.now(),s=({timestamp:o})=>{let r=o-i;r>=250&&((0,h.WG)(s),t(r-e))};return h.Gt.read(s,!0),()=>(0,h.WG)(s)}(s,250),Z.w.hasAnimatedSinceResize&&(Z.w.hasAnimatedSinceResize=!1,this.nodes.forEach(tp))})}o&&this.root.registerSharedNode(o,this),!1!==this.options.animate&&n&&(o||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:o})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||tD,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=n.getProps(),h=!this.targetLayout||!I(this.targetLayout,o)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...(0,s.rU)(r,"layout"),onPlay:a,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||tp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=o})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,h.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ty),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,l.P)(i);if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",h.Gt,!(t||i))}let{parent:o}=e;o&&!o.hasCheckedOptimisedAppear&&t(o)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(td);return}this.isUpdating||this.nodes.forEach(tc),this.isUpdating=!1,this.nodes.forEach(tm),this.nodes.forEach(tn),this.nodes.forEach(ta),this.clearAllSnapshots();let t=d.k.now();h.uv.delta=(0,g.q)(0,1e3/60,t-h.uv.timestamp),h.uv.timestamp=t,h.uv.isProcessing=!0,h.PP.update.process(h.uv),h.PP.preRender.process(h.uv),h.PP.render.process(h.uv),h.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,u.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(tu),this.sharedNodes.forEach(tf)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,h.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){h.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,$.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=o(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!c)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!X(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&(e||(0,_.HD)(this.latestValues)||o)&&(c(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),tR((e=s).x),tR(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return(0,$.ge)();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(tS))){let{scroll:t}=this.root;t&&((0,V.Ql)(i.x,t.offset.x),(0,V.Ql)(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=(0,$.ge)();if(C(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:o,options:r}=s;s!==this.root&&o&&r.layoutScroll&&(o.wasRoot&&C(i,t),(0,V.Ql)(i.x,o.offset.x),(0,V.Ql)(i.y,o.offset.y))}return i}applyTransform(t,e=!1){let i=(0,$.ge)();C(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,V.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,_.HD)(s.latestValues)&&(0,V.Ww)(i,s.latestValues)}return(0,_.HD)(this.latestValues)&&(0,V.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,$.ge)();C(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,_.HD)(i.latestValues))continue;(0,_.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,$.ge)();C(s,i.measurePageBox()),N(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,_.HD)(this.latestValues)&&N(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;let i=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=i.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=i.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=i.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==i;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:r}=this.options;if(this.layout&&(o||r)){if(this.resolvedRelativeTargetAt=h.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,$.ge)(),this.relativeTargetOrigin=(0,$.ge)(),(0,M.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),C(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,$.ge)(),this.targetWithTransforms=(0,$.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,M.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):C(this.target,this.layout.layoutBox),(0,V.o4)(this.target,this.targetDelta)):C(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,$.ge)(),this.relativeTargetOrigin=(0,$.ge)(),(0,M.jA)(this.relativeTargetOrigin,this.target,t.target),C(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tt&&J.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,_.vk)(this.parent.latestValues)||(0,_.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===h.uv.timestamp&&(s=!1),s)return;let{layout:o,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(o||r))return;C(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;(0,V.OU)(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=(0,$.ge)());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(B(this.prevProjectionDelta.x,this.projectionDelta.x),B(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,M.vb)(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&Q(this.projectionDelta.x,this.prevProjectionDelta.x)&&Q(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),tt&&J.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,$.xU)(),this.projectionDelta=(0,$.xU)(),this.projectionDeltaWithTransform=(0,$.xU)()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,o=s?s.latestValues:{},r={...this.latestValues},n=(0,$.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=(0,$.ge)(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(tT));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(tx(n.x,t.x,s),tx(n.y,t.y,s),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,m,p,g,y;(0,M.jA)(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,g=a,y=s,tP(m.x,p.x,g.x,y),tP(m.y,p.y,g.y,y),i&&(h=this.relativeTarget,c=i,G(h.x,c.x)&&G(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=(0,$.ge)()),C(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,o,r){o?(t.opacity=(0,v.k)(0,void 0!==i.opacity?i.opacity:1,S(s)),t.opacityExit=(0,v.k)(void 0!==e.opacity?e.opacity:1,0,j(s))):r&&(t.opacity=(0,v.k)(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let o=0;o<A;o++){let r=`border${D[o]}Radius`,n=w(e,r),a=w(i,r);(void 0!==n||void 0!==a)&&(n||(n=0),a||(a=0),0===n||0===a||R(n)===R(a)?(t[r]=Math.max((0,v.k)(E(n),E(a),s),0),(T.KN.test(a)||T.KN.test(n))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,v.k)(e.rotate||0,i.rotate||0,s))}(r,o,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,h.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=h.Gt.update(()=>{Z.w.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=(0,n.S)(0)?0:(0,r.OQ)(t);return s.start((0,a.f)("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:o}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&tw(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,$.ge)();let e=(0,M.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,M.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}C(e,i),(0,V.Ww)(e,o),(0,M.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,o)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new q),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&to("z",t,s,this.animationValues);for(let e=0;e<te.length;e++)to(`rotate${te[e]}`,t,s,this.animationValues),to(`skew${te[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ti;let s={visibility:""},o=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=(0,f.u)(null==t?void 0:t.pointerEvents)||"",s.transform=o?o(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,f.u)(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!(0,_.HD)(this.latestValues)&&(e.transform=o?o({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",o=t.x.translate/e.x,r=t.y.translate/e.y,n=(null==i?void 0:i.z)||0;if((o||r||n)&&(s=`translate3d(${o}px, ${r}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:o,rotateY:r,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),o&&(s+=`rotateX(${o}deg) `),r&&(s+=`rotateY(${r}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),o&&(s.transform=o(n,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!=(i=null!=(e=n.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:s.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,Y.H){if(void 0===n[t])continue;let{correct:e,applyTo:i}=Y.H[t],o="none"===s.transform?n[t]:e(n[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=o}else s[t]=o}return this.options.layoutId&&(s.pointerEvents=r===this?(0,f.u)(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(td),this.root.sharedNodes.clear()}}}function tn(t){t.updateLayout()}function ta(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:o}=t.options,r=i.source!==t.layout.source;"size"===o?(0,K.X)(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],o=(0,M.CQ)(s);s.min=e[t].min,s.max=s.min+o}):tw(o,i.layoutBox,e)&&(0,K.X)(s=>{let o=r?i.measuredBox[s]:i.layoutBox[s],n=(0,M.CQ)(e[s]);o.max=o.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,$.xU)();(0,M.vb)(n,e,i.layoutBox);let a=(0,$.xU)();r?(0,M.vb)(a,t.applyTransform(s,!0),i.measuredBox):(0,M.vb)(a,e,i.layoutBox);let l=!X(n),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:o,layout:r}=s;if(o&&r){let n=(0,$.ge)();(0,M.jA)(n,i.layoutBox,o.layoutBox);let a=(0,$.ge)();(0,M.jA)(a,e,r.layoutBox),I(n,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeTargetChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function tl(t){tt&&J.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function th(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function tu(t){t.clearSnapshot()}function td(t){t.clearMeasurements()}function tc(t){t.isLayoutDirty=!1}function tm(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function tp(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function tg(t){t.resolveTargetDelta()}function tv(t){t.calcProjection()}function ty(t){t.resetSkewAndRotation()}function tf(t){t.removeLeadSnapshot()}function tx(t,e,i){t.translate=(0,v.k)(e.translate,0,i),t.scale=(0,v.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function tP(t,e,i,s){t.min=(0,v.k)(e.min,i.min,s),t.max=(0,v.k)(e.max,i.max,s)}function tT(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let tD={duration:.45,ease:[.4,0,.1,1]},tA=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),tE=tA("applewebkit/")&&!tA("chrome/")?Math.round:o.l;function tR(t){t.min=tE(t.min),t.max=tE(t.max)}function tw(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,M.HQ)(z(e),z(i),.2)}function tS(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}var tj=i(51442);let tk=tr({attachResizeListener:(t,e)=>(0,tj.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tL={current:void 0},tC=tr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tL.current){let t=new tk({});t.mount(window),t.setOptions({layoutScroll:!0}),tL.current=t}return tL.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},64200:(t,e,i)=>{i.d(e,{CQ:()=>o,HQ:()=>r,N:()=>h,jA:()=>d,vb:()=>a});var s=i(21109);function o(t){return t.max-t.min}function r(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,r=.5){t.origin=r,t.originPoint=(0,s.k)(e.min,e.max,t.origin),t.scale=o(i)/o(e),t.translate=(0,s.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,s){n(t.x,e.x,i.x,s?s.originX:void 0),n(t.y,e.y,i.y,s?s.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+o(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+o(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},94198:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return[t("x"),t("y")]}}}]);