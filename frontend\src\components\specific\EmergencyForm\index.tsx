"use client";

import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { MapPin } from "lucide-react";
import { Button, Link, Select, SelectItem, Textarea } from "@nextui-org/react";
import {
    EmergencyApplicationSchemaType,
    emergencyApplicationSchema,
} from "@/schemas/application";
import { FileUpload } from "@/components/ui/file-upload";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

type Params = {
    token: string;
};

const locations = [
    "القدس",
    "رام الله",
    "بيت لحم",
    "الخليل",
    "نابلس",
    "جنين",
    "طولكرم",
    "قلقيلية",
    "أريحا",
];

const assistanceTypes = [
    { value: "M", text: "طبية" },
    { value: "O", text: "إغاثة" },
    { value: "D", text: "خطر" },
];

export function EmergencyForm({ token }: Params) {
    const router = useRouter();
    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
    } = useForm<EmergencyApplicationSchemaType>({
        resolver: zodResolver(emergencyApplicationSchema),
        defaultValues: {
            location: "",
            description: "",
            images: [],
        },
    });

    async function onSubmit(data: EmergencyApplicationSchemaType) {
        try {
            const formData = new FormData();

            data.images?.forEach((file: File) => {
                formData.append(`images`, file);
            });

            formData.append("location", data.location);
            formData.append("emergency_type", data.emergency_type);
            formData.append("description", data.description);

            const res = await fetch(
                `${process.env.NEXT_PUBLIC_BACKEND_URL}/emergency/create/`,
                {
                    method: "POST",
                    body: formData,
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            if (res.status === 201) {
                toast.success("تم إرسال الطلب بنجاح");
                router.refresh();
                router.push("/");
            } else {
                const data = await res.json();
                console.log(data);
                toast.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة");
            }
        } catch {
            toast.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة");
        }
    }

    const handleImageChange = (files: File[]) => {
        if (!files || files.length <= 0) return;

        const newImages = Array.from(files).filter((file) =>
            file.type.startsWith("image/")
        );
        setValue("images", newImages);
    };

    return (
        <div className="w-full max-w-2xl mx-auto p-6">
            <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold">أرسل إشعار للطوارئ</h1>
                <MapPin className="w-6 h-6 text-blue-500" />
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        حدد موقعك (إجباري) *
                    </label>
                    <Controller
                        name="location"
                        control={control}
                        render={({ field }) => (
                            <Select
                                label="من فضلك اختر موقعك"
                                {...field}
                                errorMessage={errors.location?.message}
                                isInvalid={!!errors.location}
                            >
                                {locations.map((location) => (
                                    <SelectItem key={location} value={location}>
                                        {location}
                                    </SelectItem>
                                ))}
                            </Select>
                        )}
                    />
                </div>

                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        نوع المساعدة (إجباري) *
                    </label>
                    <Controller
                        name="emergency_type"
                        control={control}
                        render={({ field }) => (
                            <Select
                                label="من فضلك اختر نوع المساعدة"
                                {...field}
                                errorMessage={errors.emergency_type?.message}
                                isInvalid={!!errors.emergency_type}
                            >
                                {assistanceTypes.map((type) => (
                                    <SelectItem
                                        key={type.value}
                                        value={type.value}
                                    >
                                        {type.text}
                                    </SelectItem>
                                ))}
                            </Select>
                        )}
                    />
                </div>

                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        ارفق صور للحالة (إجباري) *
                    </label>

                    <FileUpload isMultiple onChange={handleImageChange} />

                    {/* <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <label className="flex flex-col items-center gap-2 cursor-pointer">
                            <Upload className="w-6 h-6 text-gray-400" />
                            <span className="text-sm text-gray-500">
                                اضغط هنا لرفع الصور
                            </span>
                            <input
                                type="file"
                                accept="image/*"
                                multiple
                                className="hidden"
                                onChange={(e) => {
                                    if (!e.target.files) return;
                                    handleImageChange(
                                        Array.from(e.target.files)
                                    );
                                }}
                            />
                        </label>
                        {selectedImages.length > 0 && (
                            <div className="grid grid-cols-3 gap-4 mt-4">
                                {selectedImages.map((file, index) => (
                                    <div
                                        key={index}
                                        className="relative aspect-square"
                                    >
                                        <Image
                                            src={URL.createObjectURL(file)}
                                            alt={`Uploaded image ${index + 1}`}
                                            fill
                                            className="object-cover rounded-lg"
                                        />
                                        <Button
                                            color="danger"
                                            size="sm"
                                            variant="light"
                                            className="min-w-2 font-bold text-lg"
                                            onClick={() =>
                                                setSelectedImages((prev) =>
                                                    prev.filter(
                                                        (img) =>
                                                            img.name !==
                                                            file.name
                                                    )
                                                )
                                            }
                                        >
                                            X
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div> */}

                    {errors.images && (
                        <p className="text-xs text-danger">
                            {errors.images.message}
                        </p>
                    )}
                </div>

                <div className="space-y-2">
                    <label className="text-sm font-medium">
                        وصف الحالة (إجباري) *
                    </label>
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Textarea
                                placeholder="من فضلك اكتب وصف الحالة"
                                minRows={25}
                                {...field}
                                errorMessage={errors.description?.message}
                                isInvalid={!!errors.description}
                            />
                        )}
                    />
                </div>

                <div className="flex justify-between items-center gap-2">
                    <Button
                        as={Link}
                        href="/"
                        type="button"
                        variant="bordered"
                        color="default"
                    >
                        إلغاء
                    </Button>
                    <Button type="submit" color="primary">
                        أرسل الطلب
                    </Button>
                </div>
            </form>
        </div>
    );
}
